# User Profile System - Tickets Jira

## Ticket 1: Criar Entidade Permission

### Título
**UPS-001: Implementar Entidade Permission com Sistema de Permissões Granulares**

### Descrição
Criar uma nova entidade Permission para implementar sistema de permissões granulares no sistema. Esta entidade permitirá controle de acesso refinado através de slugs únicos e relacionamento many-to-many com Profile.

### Componentes a Implementar
- **Migration**: `xxxx_create_permissions_table.php`
- **Model**: `app/Models/Permission.php`
- **Domain**: `app/Domains/Permission.php`
- **Factory**: `app/Factories/PermissionFactory.php`
- **Repository**: `app/Repositories/PermissionRepository.php`
- **Controller**: `app/Http/Controllers/PermissionController.php`
- **Requests**: 
  - `app/Http/Requests/Permission/CreatePermissionRequest.php`
  - `app/Http/Requests/Permission/UpdatePermissionRequest.php`
- **Resource**: `app/Http/Resources/PermissionResource.php`
- **Use Cases**:
  - `app/UseCases/Permission/Create.php`
  - `app/UseCases/Permission/Update.php`
  - `app/UseCases/Permission/Delete.php`
  - `app/UseCases/Permission/GetAll.php`
  - `app/UseCases/Permission/Get.php`

### Acceptance Criteria
- [ ] Migration cria tabela `permissions` com campos: id, slug (unique), name, description (text), created_at, updated_at
- [ ] Model Permission com relacionamento many-to-many com Profile
- [ ] Domain Permission com métodos toArray(), toStoreArray(), toUpdateArray()
- [ ] Factory com métodos buildFromModel(), buildFromStoreRequest(), buildFromUpdateRequest()
- [ ] Repository com métodos fetchAll(), fetchById(), store(), update(), delete()
- [ ] Controller com endpoints CRUD (apenas super_admin pode acessar)
- [ ] Requests com validações apropriadas (slug único, campos obrigatórios)
- [ ] Resource para formatação de resposta da API
- [ ] Use Cases implementados seguindo padrão do projeto
- [ ] Validação que apenas super_admin pode criar/editar/deletar permissões

---

## Ticket 2: Melhorar Entidade Profile e Domain User

### Título
**UPS-002: Refatorar Profile para Isolamento Organizacional e Adicionar Métodos de Verificação**

### Descrição
Refatorar a entidade Profile existente para suportar isolamento por organização e adicionar métodos de verificação de permissões. Também atualizar o Domain User para incluir verificação de permissões.

### Componentes a Modificar/Criar
- **Migration**: `xxxx_add_organization_fields_to_profiles_table.php`
- **Migration**: `xxxx_create_profile_permissions_table.php` (tabela pivot)
- **Model**: `app/Models/Profile.php` (modificar)
- **Domain**: `app/Domains/Profile.php` (modificar)
- **Domain**: `app/Domains/User.php` (modificar)
- **Factory**: `app/Factories/ProfileFactory.php` (modificar)
- **Repository**: `app/Repositories/ProfileRepository.php` (modificar)
- **Use Cases**:
  - `app/UseCases/Profile/FetchByOrganization.php` (novo)
  - `app/UseCases/Profile/CreateDefaultAdmin.php` (novo)
  - `app/UseCases/Profile/CheckPermission.php` (novo)
  - `app/UseCases/User/CheckPermission.php` (novo)

### Acceptance Criteria
- [ ] Migration adiciona `organization_id` (nullable), índices apropriados na tabela profiles
- [ ] Migration cria tabela pivot `profile_permissions` com profile_id, permission_id, created_at, updated_at
- [ ] Model Profile atualizado com relacionamento belongsTo Organization e belongsToMany Permission
- [ ] Domain Profile com métodos: isAdmin(), isSuperAdmin(), can($slug), hasPermission($slug)
- [ ] Domain User com método can($slug) que verifica através do profile
- [ ] Factory atualizado para incluir organization_id nos métodos
- [ ] Repository com método fetchByOrganization($organizationId)
- [ ] Use Case CreateDefaultAdmin cria perfil admin automaticamente para nova organização
- [ ] Use Case FetchByOrganization retorna apenas perfis da organização específica
- [ ] Use Cases CheckPermission implementados para User e Profile
- [ ] Validação que super_admin não precisa de organization_id
- [ ] Validação que apenas um admin por organização é permitido

---

## Ticket 3: Criar Endpoints de Gerenciamento de Profile

### Título
**UPS-003: Implementar API Completa para Gerenciamento de Profiles**

### Descrição
Criar endpoints completos para gerenciamento de profiles, incluindo CRUD operations e endpoints específicos para verificação de permissões de profiles.

### Componentes a Implementar/Modificar
- **Controller**: `app/Http/Controllers/ProfileController.php` (modificar)
- **Requests**:
  - `app/Http/Requests/Profile/CreateProfileRequest.php`
  - `app/Http/Requests/Profile/UpdateProfileRequest.php`
- **Resource**: `app/Http/Resources/ProfileResource.php`
- **Use Cases**:
  - `app/UseCases/Profile/Store.php`
  - `app/UseCases/Profile/Update.php`
  - `app/UseCases/Profile/Delete.php`
- **Routes**: Adicionar rotas em `routes/api.php`

### Endpoints a Implementar
- `GET /api/profiles` - Listar perfis da organização
- `POST /api/profiles` - Criar novo perfil
- `GET /api/profiles/{id}` - Buscar perfil específico
- `PUT /api/profiles/{id}` - Atualizar perfil
- `DELETE /api/profiles/{id}` - Deletar perfil
- `GET /api/profile/permissions` - Listar permissões do perfil atual
- `GET /api/profile/permission/{slug}/can` - Verificar permissão específica do perfil

### Acceptance Criteria
- [ ] Endpoint GET /profiles retorna apenas perfis da organização do usuário autenticado
- [ ] Endpoint POST /profiles cria perfil vinculado à organização do usuário
- [ ] Endpoint PUT /profiles/{id} valida que perfil pertence à organização do usuário
- [ ] Endpoint DELETE /profiles/{id} impede deleção de perfil admin
- [ ] Endpoint GET /profile/permissions retorna permissões do perfil do usuário autenticado
- [ ] Endpoint GET /profile/permission/{slug}/can retorna boolean indicando se perfil tem permissão
- [ ] Validação que usuários não podem criar perfis admin (apenas sistema)
- [ ] Validação que usuários não podem criar perfis super_admin
- [ ] Requests com validações apropriadas (nome obrigatório, organização válida)
- [ ] Resource formatando resposta com todos os campos necessários
- [ ] Use Cases seguindo padrão do projeto com tratamento de erros

---

## Ticket 4: Criar Endpoints de Verificação de User

### Título
**UPS-004: Implementar Endpoints de Verificação de Permissões para Users**

### Descrição
Criar endpoints específicos para verificação de permissões de usuários, incluindo listagem de permissões e verificação individual de permissões.

### Componentes a Implementar
- **Controller**: `app/Http/Controllers/UserPermissionController.php`
- **Resource**: `app/Http/Resources/UserPermissionResource.php`
- **Use Cases**:
  - `app/UseCases/User/GetPermissions.php`
  - `app/UseCases/User/CheckSpecificPermission.php`
- **Routes**: Adicionar rotas em `routes/api.php`

### Endpoints a Implementar
- `GET /api/user/permissions` - Listar todas as permissões do usuário
- `GET /api/user/permission/{slug}/can` - Verificar permissão específica do usuário

### Acceptance Criteria
- [ ] Endpoint GET /user/permissions retorna todas as permissões do usuário autenticado
- [ ] Endpoint GET /user/permission/{slug}/can retorna boolean indicando se usuário tem permissão
- [ ] Usuários admin retornam true para qualquer permissão dentro de sua organização
- [ ] Usuários super_admin retornam true para qualquer permissão
- [ ] Usuários normais retornam apenas permissões específicas do seu perfil
- [ ] Resource formatando lista de permissões com slug, name e description
- [ ] Use Cases com validação de usuário autenticado
- [ ] Tratamento de erro para permissões inexistentes
- [ ] Resposta consistente em formato JSON
- [ ] Logs de auditoria para verificações de permissão

---

## Ticket 5: Criar Endpoint de Gerenciamento de Permissões de Profile

### Título
**UPS-005: Implementar Endpoints para Adicionar/Remover Permissões de Profiles**

### Descrição
Criar endpoints para gerenciar permissões de profiles, permitindo adicionar e remover permissões específicas de profiles existentes.

### Componentes a Implementar
- **Controller**: `app/Http/Controllers/ProfilePermissionController.php`
- **Requests**:
  - `app/Http/Requests/Profile/AddPermissionRequest.php`
  - `app/Http/Requests/Profile/RemovePermissionRequest.php`
- **Use Cases**:
  - `app/UseCases/Profile/AddPermission.php`
  - `app/UseCases/Profile/RemovePermission.php`
- **Routes**: Adicionar rotas em `routes/api.php`

### Endpoints a Implementar
- `POST /api/profile/permission/add` - Adicionar permissão a um perfil
- `POST /api/profile/permission/remove` - Remover permissão de um perfil

### Acceptance Criteria
- [ ] Endpoint POST /profile/permission/add adiciona permissão específica a um perfil
- [ ] Endpoint POST /profile/permission/remove remove permissão específica de um perfil
- [ ] Validação que usuário tem permissão para gerenciar permissões de profiles
- [ ] Validação que profile pertence à organização do usuário (exceto super_admin)
- [ ] Validação que permission existe antes de adicionar/remover
- [ ] Prevenção de duplicação ao adicionar permissão já existente
- [ ] Tratamento de erro ao remover permissão inexistente
- [ ] Requests com validações: profile_id obrigatório, permission_slug obrigatório
- [ ] Use Cases com transações para garantir consistência
- [ ] Logs de auditoria para mudanças de permissões
- [ ] Resposta indicando sucesso/falha da operação

---

## Ticket 6: Implementar Sistema de Criação Automática de Profile Admin

### Título
**UPS-006: Implementar Criação Automática de Profile Admin na Criação de Organizações**

### Descrição
Implementar sistema que automaticamente cria um perfil admin quando uma nova organização é registrada, e associa o usuário registrante a esse perfil.

### Componentes a Modificar/Criar
- **Use Case**: `app/UseCases/Organization/Register.php` (modificar)
- **Use Case**: `app/UseCases/Profile/CreateDefaultAdmin.php`
- **Domain**: `app/Domains/Organization.php` (modificar)
- **Repository**: `app/Repositories/OrganizationRepository.php` (modificar)

### Acceptance Criteria
- [ ] Ao criar nova organização, automaticamente cria perfil admin
- [ ] Perfil admin criado com nome "Administrador", slug "admin", is_admin=true
- [ ] Usuário que registra a organização é automaticamente associado ao perfil admin
- [ ] Validação que apenas um perfil admin existe por organização
- [ ] Transação garantindo que organização e perfil admin são criados juntos
- [ ] Rollback automático se criação do perfil admin falhar
- [ ] Logs de auditoria para criação de perfil admin
- [ ] Teste que verifica criação automática do perfil
- [ ] Teste que verifica associação do usuário ao perfil admin
- [ ] Tratamento de erro se perfil admin já existir

---

## Ticket 7: Criar Testes Unitários e de Integração

### Título
**UPS-007: Implementar Cobertura Completa de Testes para Sistema de Profiles e Permissões**

### Descrição
Criar testes unitários e de integração completos para todo o sistema de profiles e permissões, garantindo qualidade e confiabilidade do código.

### Componentes de Teste a Criar
- **Unit Tests**:
  - `tests/Unit/Domains/PermissionTest.php`
  - `tests/Unit/Domains/ProfileTest.php`
  - `tests/Unit/Domains/UserTest.php`
  - `tests/Unit/Factories/PermissionFactoryTest.php`
  - `tests/Unit/Factories/ProfileFactoryTest.php`
  - `tests/Unit/Repositories/PermissionRepositoryTest.php`
  - `tests/Unit/Repositories/ProfileRepositoryTest.php`
- **Feature Tests**:
  - `tests/Feature/Controllers/PermissionControllerTest.php`
  - `tests/Feature/Controllers/ProfileControllerTest.php`
  - `tests/Feature/Controllers/UserPermissionControllerTest.php`
  - `tests/Feature/Controllers/ProfilePermissionControllerTest.php`
  - `tests/Feature/UseCases/Permission/CreateTest.php`
  - `tests/Feature/UseCases/Profile/CreateDefaultAdminTest.php`
  - `tests/Feature/UseCases/User/CheckPermissionTest.php`

### Acceptance Criteria
- [ ] Testes unitários para todos os métodos dos Domains (can(), isAdmin(), isSuperAdmin())
- [ ] Testes unitários para todos os métodos das Factories (buildFromModel(), etc.)
- [ ] Testes unitários para todos os métodos dos Repositories
- [ ] Testes de integração para todos os endpoints da API
- [ ] Testes de integração para todos os Use Cases
- [ ] Testes verificando isolamento de dados entre organizações
- [ ] Testes verificando que super_admin pode acessar tudo
- [ ] Testes verificando que admin pode acessar apenas sua organização
- [ ] Testes verificando que usuários normais só acessam suas permissões
- [ ] Testes de criação automática de perfil admin
- [ ] Testes de validação de permissões em endpoints
- [ ] Cobertura de testes > 90% para todos os componentes críticos
- [ ] Testes usando factories sem dependências circulares (app()->make())
- [ ] Mocks apenas para dependências externas, objetos reais para lógica interna

---

## Ticket 8: Implementar Middleware de Autorização

### Título
**UPS-008: Criar Middleware para Verificação Automática de Permissões**

### Descrição
Implementar middleware que automaticamente verifica permissões baseado em rotas e ações, facilitando a proteção de endpoints.

### Componentes a Implementar
- **Middleware**: `app/Http/Middleware/CheckPermission.php`
- **Middleware**: `app/Http/Middleware/CheckSuperAdmin.php`
- **Middleware**: `app/Http/Middleware/CheckAdmin.php`
- **Provider**: Registrar middlewares em `app/Http/Kernel.php`

### Acceptance Criteria
- [ ] Middleware CheckPermission verifica permissão específica via parâmetro
- [ ] Middleware CheckSuperAdmin permite apenas usuários super_admin
- [ ] Middleware CheckAdmin permite admin e super_admin
- [ ] Middlewares registrados e disponíveis para uso em rotas
- [ ] Resposta 403 Forbidden para usuários sem permissão
- [ ] Resposta 401 Unauthorized para usuários não autenticados
- [ ] Logs de tentativas de acesso negado
- [ ] Testes para todos os middlewares
- [ ] Documentação de uso dos middlewares
- [ ] Aplicação dos middlewares nas rotas apropriadas
