# Epic: User Profile System - Sistema de Perfis e Permissões Modular

## Overview

Esta Epic tem como objetivo implementar um sistema completo de perfis de usuários e permissões granulares, modularizando o controle de acesso da aplicação através de perfis organizacionais e permissões específicas. O sistema atual possui uma entidade Profile básica que é compartilhada entre todas as organizações, o que não atende às necessidades de isolamento e controle granular necessários para um sistema multi-tenant robusto.

### Funcionalidades Atuais Identificadas:
- ✅ Entidade Profile básica existente
- ✅ Sistema de autenticação de usuários
- ✅ Relacionamento User-Organization
- ⚠️ Profile compartilhado entre organizações (precisa ser isolado)
- ❌ Sistema de permissões granulares
- ❌ Profile super_admin para usuários internos
- ❌ Profile admin automático na criação de organizações
- ❌ Endpoints para gerenciamento de perfis e permissões
- ❌ Métodos de verificação de permissões nos domínios

### Melhorias Propostas:

#### 1. **Isolamento de Perfis por Organização**
Transformar o sistema atual de perfis compartilhados em um sistema isolado por organização, onde cada organização possui seus próprios perfis, garantindo segurança e flexibilidade no controle de acesso.

#### 2. **Sistema de Permissões Granulares**
Implementar uma entidade Permission com relacionamento many-to-many com Profile, permitindo controle granular de acesso através de slugs únicos de permissões.

#### 3. **Perfis Especiais (Admin e Super Admin)**
Criar perfis especiais com privilégios elevados: admin (controle total dentro da organização) e super_admin (controle total do sistema, apenas para usuários internos).

#### 4. **API Completa de Gerenciamento**
Desenvolver endpoints completos para criação, edição e gerenciamento de perfis e permissões, incluindo verificações de acesso e listagem de permissões.

## Resumo do Plano de Implementação

A Epic será implementada em 4 fases sequenciais, cada uma construindo sobre a anterior para garantir estabilidade e funcionalidade incremental.

**Fase 1**: Reestruturação da Entidade Profile - Isolamento por organização e criação de perfis especiais
**Fase 2**: Implementação do Sistema de Permissões - Criação da entidade Permission e relacionamentos
**Fase 3**: Métodos de Verificação - Implementação dos métodos can() nos domínios User e Profile
**Fase 4**: API e Endpoints - Desenvolvimento completo dos endpoints de gerenciamento

## Plano de Implementação Detalhado

### 1. Reestruturação da Entidade Profile

#### Database:
- **Tabela `profiles`**: Adicionar campo `organization_id` (nullable para super_admin), `is_admin` (boolean), `is_super_admin` (boolean default false)
- **Migration**: Adicionar campos necessários e índices para performance

#### Domínios:
- **Profile**: Adicionar métodos `isAdmin()`, `isSuperAdmin()`, `can($slug)`, relacionamento com Organization
- **Organization**: Método `createDefaultAdminProfile()` para criação automática do perfil admin

#### Usecases:
- **Profile/CreateDefaultAdmin**: Criar perfil admin padrão para nova organização
- **Profile/FetchByOrganization**: Buscar perfis específicos de uma organização

### 2. Implementação do Sistema de Permissões

#### Rotas/Endpoints Necessários:
- `POST /api/permissions` - Criar nova permissão (apenas super_admin)
- `GET /api/permissions` - Listar todas as permissões
- `PUT /api/permissions/{id}` - Editar permissão (apenas super_admin)
- `DELETE /api/permissions/{id}` - Deletar permissão (apenas super_admin)

#### Database:
- **Tabela `permissions`**: Campos: id, slug (unique), name, description (text), created_at, updated_at
- **Tabela `profile_permissions`**: Relacionamento many-to-many. Campos: profile_id, permission_id, created_at, updated_at

#### Domínios:
- **Permission**: Domínio para gerenciar permissões do sistema
- **Profile**: Adicionar relacionamento hasMany com Permission através de pivot

#### Usecases:
- **Permission/Create**: Criar nova permissão (validação super_admin)
- **Permission/Update**: Atualizar permissão existente
- **Permission/Delete**: Remover permissão do sistema
- **Permission/FetchAll**: Listar todas as permissões disponíveis

### 3. Métodos de Verificação

#### Domínios:
- **User**: Método `can($slug)` que verifica através do profile do usuário
- **Profile**: Método `can($slug)` que verifica permissões específicas ou status admin/super_admin

#### Usecases:
- **User/CheckPermission**: Verificar se usuário possui permissão específica
- **Profile/CheckPermission**: Verificar se perfil possui permissão específica
- **Profile/AddPermission**: Adicionar permissão a um perfil
- **Profile/RemovePermission**: Remover permissão de um perfil

### 4. API e Endpoints

#### Rotas/Endpoints Necessários:
- `GET /api/user/permission/{slug}/can` - Verificar permissão específica do usuário
- `GET /api/profile/permission/{slug}/can` - Verificar permissão específica do perfil
- `GET /api/user/permissions` - Listar permissões do usuário
- `GET /api/profile/permissions` - Listar permissões do perfil
- `POST /api/profile/permission/add` - Adicionar permissão a perfil
- `POST /api/profile/permission/remove` - Remover permissão de perfil
- `GET /api/profiles` - Listar perfis da organização
- `POST /api/profiles` - Criar novo perfil
- `PUT /api/profiles/{id}` - Editar perfil
- `DELETE /api/profiles/{id}` - Deletar perfil

#### Controllers:
- **ProfileController**: Gerenciamento completo de perfis
- **PermissionController**: Gerenciamento de permissões (apenas super_admin)

#### Requests:
- **CreateProfileRequest**: Validação para criação de perfis
- **UpdateProfileRequest**: Validação para atualização de perfis
- **CreatePermissionRequest**: Validação para criação de permissões
- **AddPermissionToProfileRequest**: Validação para adicionar permissões

## Previsão de PR

### Models
```
app/Models/Permission.php (novo)
app/Models/Profile.php (modificado)
```

### Domains
```
app/Domains/Core/Permission.php (novo)
app/Domains/Core/Profile.php (modificado)
app/Domains/Core/User.php (modificado)
app/Domains/Core/Organization.php (modificado)
```

### Factories
```
app/Factories/Core/PermissionFactory.php (novo)
app/Factories/Core/ProfileFactory.php (modificado)
```

### Repositories
```
app/Repositories/PermissionRepository.php (novo)
app/Repositories/ProfileRepository.php (modificado)
```

### Use Cases
```
app/UseCases/Core/Profile/CreateDefaultAdmin.php (novo)
app/UseCases/Core/Profile/FetchByOrganization.php (novo)
app/UseCases/Core/Profile/CheckPermission.php (novo)
app/UseCases/Core/Profile/AddPermission.php (novo)
app/UseCases/Core/Profile/RemovePermission.php (novo)
app/UseCases/Core/Permission/Create.php (novo)
app/UseCases/Core/Permission/Update.php (novo)
app/UseCases/Core/Permission/Delete.php (novo)
app/UseCases/Core/Permission/FetchAll.php (novo)
app/UseCases/Core/User/CheckPermission.php (novo)
```

### Controllers
```
app/Http/Controllers/Core/ProfileController.php (novo)
app/Http/Controllers/Core/PermissionController.php (novo)
```

### Requests
```
app/Http/Requests/Profile/CreateProfileRequest.php (novo)
app/Http/Requests/Profile/UpdateProfileRequest.php (novo)
app/Http/Requests/Permission/CreatePermissionRequest.php (novo)
app/Http/Requests/Permission/AddPermissionToProfileRequest.php (novo)
```

### Migrations
```
database/migrations/xxxx_add_organization_fields_to_profiles_table.php (novo)
database/migrations/xxxx_create_permissions_table.php (novo)
database/migrations/xxxx_create_profile_permissions_table.php (novo)
```

### Routes
```
routes/api.php (modificado - adicionar novas rotas)
```

**Total Estimado: ~25 arquivos (18 novos + 7 modificados)**

## Melhorias Específicas Identificadas

### Problema 1: Perfis Compartilhados Entre Organizações
**Situação Atual**: O sistema atual permite que perfis sejam compartilhados entre diferentes organizações, criando riscos de segurança e falta de isolamento
**Impacto**: Possibilidade de vazamento de dados entre organizações e dificuldade de gerenciamento granular de permissões
**Solução**: Isolamento completo de perfis por organização através do campo organization_id

### Problema 2: Ausência de Sistema de Permissões Granulares
**Situação Atual**: Não existe um sistema estruturado de permissões específicas, limitando o controle de acesso
**Impacto**: Impossibilidade de criar controles de acesso refinados e personalizados por funcionalidade
**Solução**: Implementação da entidade Permission com relacionamento many-to-many com Profile

### Problema 3: Falta de Perfis Administrativos Especiais
**Situação Atual**: Não existem perfis especiais para administradores de organização ou super administradores do sistema
**Impacto**: Dificuldade de gerenciamento e falta de controles administrativos adequados
**Solução**: Criação de perfis admin (por organização) e super_admin (sistema global)

### Problema 4: Ausência de API de Gerenciamento
**Situação Atual**: Não existem endpoints para gerenciar perfis e permissões de forma programática
**Impacto**: Impossibilidade de criar interfaces de usuário para gerenciamento de acesso
**Solução**: Desenvolvimento de API completa com todos os endpoints necessários

## Plano de Testes

### Testes Unitários:
- Métodos can() nos domínios User e Profile
- Validações de criação e atualização de perfis e permissões
- Relacionamentos entre Profile, Permission e Organization
- Regras de negócio para perfis admin e super_admin

### Testes de Integração:
- Fluxo completo de criação de organização com perfil admin
- Adição e remoção de permissões de perfis
- Verificação de permissões através dos endpoints
- Isolamento de dados entre organizações

### Testes de Performance:
- Consultas de verificação de permissões com grande volume de dados
- Listagem de perfis e permissões com paginação
- Relacionamentos many-to-many com performance otimizada

### Testes de Regressão:
- Funcionalidades existentes de autenticação mantidas
- Compatibilidade com dados de perfis existentes
- APIs existentes continuam funcionando após migração

## Conclusão

Esta Epic transformará fundamentalmente o sistema de controle de acesso da aplicação, implementando um modelo robusto e escalável de perfis e permissões. O sistema resultante proporcionará isolamento completo entre organizações, controle granular de permissões e perfis administrativos especiais para diferentes níveis de acesso.

### Benefícios Esperados:
- **Segurança**: Isolamento completo de dados entre organizações através de perfis específicos
- **Flexibilidade**: Sistema de permissões granulares permitindo controle refinado de acesso
- **Escalabilidade**: Arquitetura preparada para crescimento e novas funcionalidades
- **Usabilidade**: API completa para desenvolvimento de interfaces de gerenciamento

### Impacto no Negócio:
- Melhoria significativa na segurança e conformidade do sistema
- Possibilidade de criar planos diferenciados baseados em permissões
- Redução de riscos relacionados a vazamento de dados entre organizações
- Facilidade de gerenciamento e auditoria de acessos

### Métricas de Sucesso:
- 100% de isolamento entre organizações validado por testes
- Tempo de resposta < 100ms para verificações de permissão
- Cobertura de testes > 90% para todos os componentes críticos

## Referências

- Laravel Documentation - Eloquent Relationships
- Laravel Documentation - Authorization
- Best Practices for Multi-Tenant Applications
- RBAC (Role-Based Access Control) Design Patterns
- Epic Template - storage/docs/schedule/EPICs/epic-template.md
