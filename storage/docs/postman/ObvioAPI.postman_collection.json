{"info": {"_postman_id": "6b191c3f-2de2-4a50-9192-7960dc1113c5", "name": "Obvio API - Complete Collection", "description": "Complete API collection for Obvio system with organized endpoints, proper filters, and comprehensive request examples.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "5508133", "_collection_link": "https://zona51.postman.co/workspace/Zona51~65e22124-fc5f-446b-9523-2ad13d170a45/collection/15215157-6b191c3f-2de2-4a50-9192-7960dc1113c5?action=share&source=collection_link&creator=5508133"}, "item": [{"name": "🔐 Authentication", "description": "Authentication endpoints including login, register, password reset, and user session management", "item": [{"name": "Delete", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "confirmation", "value": "1", "type": "text"}]}, "url": {"raw": "{{URL}}/user/delete", "host": ["{{URL}}"], "path": ["user", "delete"]}}, "response": []}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(responseBody);\r", "if(jsonData.status === \"success\"){\r", "    postman.setEnvironmentVariable(\"TOKEN\", jsonData.data.token);\r", "}"], "type": "text/javascript", "packages": []}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/login", "host": ["{{URL}}"], "path": ["login"]}}, "response": []}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/logout", "host": ["{{URL}}"], "path": ["logout"]}}, "response": []}, {"name": "Logout All Sessions", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/logout_all_sessions", "host": ["{{URL}}"], "path": ["logout_all_sessions"]}}, "response": []}, {"name": "Password Reset - Forgot", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/auth/password/forgot", "host": ["{{URL}}"], "path": ["auth", "password", "forgot"]}}, "response": []}, {"name": "Password Reset - Reset", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"token\": \"reset_token_here\",\n    \"password\": \"newpassword123\",\n    \"password_confirmation\": \"newpassword123\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/auth/password/reset", "host": ["{{URL}}"], "path": ["auth", "password", "reset"]}}, "response": []}, {"name": "Password Reset - <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"token\": \"reset_token_here\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/auth/password/validate-token", "host": ["{{URL}}"], "path": ["auth", "password", "validate-token"]}}, "response": []}, {"name": "Register", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(responseBody);\r", "if(jsonData.status === \"success\"){\r", "    postman.setEnvironmentVariable(\"TOKEN\", jsonData.data.token);\r", "}"], "type": "text/javascript", "packages": []}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"profile_id\": 1,\n    \"first_name\": \"<PERSON>\",\n    \"last_name\": \"<PERSON><PERSON>\",\n    \"username\": \"joh<PERSON><PERSON>\",\n    \"password\": \"password123\",\n    \"password_confirmation\": \"password123\",\n    \"organization\": {\n        \"name\": \"My Company\",\n        \"email\": \"<EMAIL>\",\n        \"cpfCnpj\": \"***********\",\n        \"companyType\": \"LTDA\",\n        \"phone\": \"***********\",\n        \"mobilePhone\": \"***********\",\n        \"address\": \"Rua Example, 123\",\n        \"addressNumber\": \"123\",\n        \"complement\": \"Sala 1\",\n        \"province\": \"Centro\",\n        \"postalCode\": \"********\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/register", "host": ["{{URL}}"], "path": ["register"]}}, "response": []}, {"name": "User", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/user", "host": ["{{URL}}"], "path": ["user"]}}, "response": []}]}, {"name": "👥 User Management", "description": "User, organization, and profile management endpoints", "item": [{"name": "🏢 Organizations", "item": [{"name": "Create Organization (Unavailable)", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/organizations", "host": ["{{URL}}"], "path": ["organizations"]}}, "response": []}, {"name": "Delete Organization (Unavailable)", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/organizations/1", "host": ["{{URL}}"], "path": ["organizations", "1"]}}, "response": []}, {"name": "Get All Organizations", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/organizations?order_by=name&order_direction=asc", "host": ["{{URL}}"], "path": ["organizations"], "query": [{"key": "order_by", "value": "name", "description": "Order by field"}, {"key": "order_direction", "value": "asc", "description": "Order direction"}]}}, "response": []}, {"name": "Get Organization", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/organizations/1", "host": ["{{URL}}"], "path": ["organizations", "1"]}}, "response": []}, {"name": "Update Organization", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Company Name\",\n  \"description\": \"Updated company description\",\n  \"email\": \"<EMAIL>\",\n  \"cpf_cnpj\": \"98765432101\",\n  \"company_type\": \"SA\",\n  \"phone\": \"***********\",\n  \"mobile_phone\": \"11777777777\",\n  \"address\": \"Rua Updated, 456\",\n  \"address_number\": \"456\",\n  \"complement\": \"Sala 2\",\n  \"province\": \"Centro Novo\",\n  \"postal_code\": \"87654321\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/organizations/1", "host": ["{{URL}}"], "path": ["organizations", "1"]}}, "response": []}], "description": "Organization management - read-only operations (store/update/delete unavailable)"}, {"name": "👔 Profiles", "item": [{"name": "Create Profile (Unavailable)", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/profiles", "host": ["{{URL}}"], "path": ["profiles"]}}, "response": []}, {"name": "Delete Profile (Unavailable)", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/profiles/1", "host": ["{{URL}}"], "path": ["profiles", "1"]}}, "response": []}, {"name": "Get All Profiles", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/profiles?order_by=name&order_direction=asc", "host": ["{{URL}}"], "path": ["profiles"], "query": [{"key": "order_by", "value": "name", "description": "Order by field"}, {"key": "order_direction", "value": "asc", "description": "Order direction"}]}}, "response": []}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/profiles/1", "host": ["{{URL}}"], "path": ["profiles", "1"]}}, "response": []}, {"name": "Update Profile (Unavailable)", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/profiles/1", "host": ["{{URL}}"], "path": ["profiles", "1"]}}, "response": []}], "description": "Profile management - read-only operations (store/update/delete unavailable)"}, {"name": "👤 Users", "item": [{"name": "Create User", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"profile_id\": 1,\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"username\": \"joh<PERSON><PERSON>\",\n  \"password\": \"password123\",\n  \"password_confirmation\": \"password123\"\n}"}, "url": {"raw": "{{URL}}/users", "host": ["{{URL}}"], "path": ["users"]}}, "response": []}, {"name": "Delete User", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/users/1", "host": ["{{URL}}"], "path": ["users", "1"]}}, "response": []}, {"name": "Get All Notifications", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/notifications/all", "host": ["{{URL}}"], "path": ["notifications", "all"]}}, "response": []}, {"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/users?name=&email=&order_by=created_at&order_direction=desc", "host": ["{{URL}}"], "path": ["users"], "query": [{"key": "name", "value": "", "description": "Filter by first_name or last_name"}, {"key": "email", "value": "", "description": "Filter by email"}, {"key": "order_by", "value": "created_at", "description": "Order by field"}, {"key": "order_direction", "value": "desc", "description": "Order direction"}]}}, "response": []}, {"name": "Get Unread Notifications", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/notifications/unread", "host": ["{{URL}}"], "path": ["notifications", "unread"]}}, "response": []}, {"name": "Get User", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/users/1", "host": ["{{URL}}"], "path": ["users", "1"]}}, "response": []}, {"name": "Read All Notifications", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/notifications/read", "host": ["{{URL}}"], "path": ["notifications", "read"]}}, "response": []}, {"name": "Read Single Notification", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/notifications/read-one/1", "host": ["{{URL}}"], "path": ["notifications", "read-one", "1"]}}, "response": []}, {"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"first_name\": \"John Updated\",\n  \"last_name\": \"<PERSON><PERSON> Updated\",\n  \"username\": \"joh<PERSON><PERSON>_updated\",\n  \"password\": \"newpassword123\",\n  \"password_confirmation\": \"newpassword123\"\n}"}, "url": {"raw": "{{URL}}/users/1", "host": ["{{URL}}"], "path": ["users", "1"]}}, "response": []}], "description": "User management - CRUD operations and notifications"}]}, {"name": "🔗 Webhooks", "description": "Webhook endpoints for external integrations (Telegram, WhatsApp)", "item": [{"name": "📱 Telegram", "item": [{"name": "Receive Custom Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"update_id\": *********,\n    \"message\": {\n        \"message_id\": 1,\n        \"from\": {\n            \"id\": *********,\n            \"is_bot\": false,\n            \"first_name\": \"<PERSON>\",\n            \"username\": \"johndoe\"\n        },\n        \"chat\": {\n            \"id\": *********,\n            \"first_name\": \"<PERSON>\",\n            \"username\": \"johndoe\",\n            \"type\": \"private\"\n        },\n        \"date\": **********,\n        \"text\": \"Hello Custom Bot!\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/telegram/{{bot_id}}/receive", "host": ["{{URL}}"], "path": ["telegram", "{{bot_id}}", "receive"], "variable": [{"key": "bot_id", "value": "1", "description": "The ID of the bot"}]}}, "response": []}, {"name": "Receive Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"update_id\": *********,\n    \"message\": {\n        \"message_id\": 1,\n        \"from\": {\n            \"id\": *********,\n            \"is_bot\": false,\n            \"first_name\": \"<PERSON>\",\n            \"username\": \"johndoe\"\n        },\n        \"chat\": {\n            \"id\": *********,\n            \"first_name\": \"<PERSON>\",\n            \"username\": \"johndoe\",\n            \"type\": \"private\"\n        },\n        \"date\": **********,\n        \"text\": \"Hello Bot!\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/telegram/receive-message", "host": ["{{URL}}"], "path": ["telegram", "receive-message"]}}, "response": []}], "description": "Telegram bot webhook endpoints"}, {"name": "💬 WhatsApp", "item": [{"name": "Receive Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"object\": \"whatsapp_business_account\",\n    \"entry\": [\n        {\n            \"id\": \"WHATSAPP_BUSINESS_ACCOUNT_ID\",\n            \"changes\": [\n                {\n                    \"value\": {\n                        \"messaging_product\": \"whatsapp\",\n                        \"metadata\": {\n                            \"display_phone_number\": \"***********\",\n                            \"phone_number_id\": \"*********\"\n                        },\n                        \"contacts\": [\n                            {\n                                \"profile\": {\n                                    \"name\": \"<PERSON>\"\n                                },\n                                \"wa_id\": \"55***********\"\n                            }\n                        ],\n                        \"messages\": [\n                            {\n                                \"from\": \"55***********\",\n                                \"id\": \"wamid.xxx\",\n                                \"timestamp\": \"**********\",\n                                \"text\": {\n                                    \"body\": \"Hello WhatsApp Bot!\"\n                                },\n                                \"type\": \"text\"\n                            }\n                        ]\n                    },\n                    \"field\": \"messages\"\n                }\n            ]\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/whatsapp/webhook", "host": ["{{URL}}"], "path": ["whatsapp", "webhook"]}}, "response": []}, {"name": "Receive Message (Alternative)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"object\": \"whatsapp_business_account\",\n    \"entry\": [\n        {\n            \"id\": \"WHATSAPP_BUSINESS_ACCOUNT_ID\",\n            \"changes\": [\n                {\n                    \"value\": {\n                        \"messaging_product\": \"whatsapp\",\n                        \"metadata\": {\n                            \"display_phone_number\": \"***********\",\n                            \"phone_number_id\": \"*********\"\n                        },\n                        \"contacts\": [\n                            {\n                                \"profile\": {\n                                    \"name\": \"<PERSON>\"\n                                },\n                                \"wa_id\": \"55***********\"\n                            }\n                        ],\n                        \"messages\": [\n                            {\n                                \"from\": \"55***********\",\n                                \"id\": \"wamid.xxx\",\n                                \"timestamp\": \"**********\",\n                                \"text\": {\n                                    \"body\": \"Hello WhatsApp Bot (Alternative)!\"\n                                },\n                                \"type\": \"text\"\n                            }\n                        ]\n                    },\n                    \"field\": \"messages\"\n                }\n            ]\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/whatsapp/webhook/", "host": ["{{URL}}"], "path": ["whatsapp", "webhook", ""]}}, "response": []}, {"name": "Test WhatsApp Token", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/whatsapp/testWhatsAppToken/{{phone_number_id}}", "host": ["{{URL}}"], "path": ["whatsapp", "testWhatsAppToken", "{{phone_number_id}}"], "variable": [{"key": "phone_number_id", "value": "*********", "description": "WhatsApp Phone Number ID"}]}}, "response": []}, {"name": "Webhook Verify", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{URL}}/whatsapp/webhook?hub.mode=subscribe&hub.challenge={{challenge_token}}&hub.verify_token={{verify_token}}", "host": ["{{URL}}"], "path": ["whatsapp", "webhook"], "query": [{"key": "hub.mode", "value": "subscribe", "description": "Webhook verification mode"}, {"key": "hub.challenge", "value": "{{challenge_token}}", "description": "Challenge token from <PERSON><PERSON>"}, {"key": "hub.verify_token", "value": "{{verify_token}}", "description": "Your webhook verify token"}]}}, "response": []}, {"name": "Webhook Verify (Alternative)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{URL}}/whatsapp/webhook/?hub.mode=subscribe&hub.challenge={{challenge_token}}&hub.verify_token={{verify_token}}", "host": ["{{URL}}"], "path": ["whatsapp", "webhook", ""], "query": [{"key": "hub.mode", "value": "subscribe", "description": "Webhook verification mode"}, {"key": "hub.challenge", "value": "{{challenge_token}}", "description": "Challenge token from <PERSON><PERSON>"}, {"key": "hub.verify_token", "value": "{{verify_token}}", "description": "Your webhook verify token (uses services.meta.webhook_verify_token config)"}]}}, "response": []}], "description": "WhatsApp webhook endpoints for Meta Business API"}]}, {"name": "📥 Import", "description": "Import management endpoints for bulk data import from files (Excel, CSV) for various models like products, brands, clients, projects, budgets, stocks, etc.", "item": [{"name": "Create Import", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "description": "Excel/CSV file to import (max 8MB, extensions: jpg,jpeg,csv,xls,xlsx,gif,png,pdf,doc,docx,txt,txtx,text)", "type": "file", "src": []}, {"key": "model", "value": "products", "description": "Model to import data into (products, brands, groups, clients, projects, budgets, stocks, stock_entries, stock_exits)", "type": "text"}]}, "url": {"raw": "{{URL}}/imports", "host": ["{{URL}}"], "path": ["imports"]}}, "response": []}, {"name": "Delete Import", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/imports/{{import_id}}", "host": ["{{URL}}"], "path": ["imports", "{{import_id}}"], "variable": [{"key": "import_id", "value": "1", "description": "The ID of the import"}]}}, "response": []}, {"name": "Get All Imports", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/imports", "host": ["{{URL}}"], "path": ["imports"]}}, "response": []}, {"name": "Get Import", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/imports/{{import_id}}", "host": ["{{URL}}"], "path": ["imports", "{{import_id}}"], "variable": [{"key": "import_id", "value": "1", "description": "The ID of the import"}]}}, "response": []}, {"name": "Process Import", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "skip_header", "value": "1", "description": "Whether to skip the first row (header) when processing (1 = true, 0 = false)", "type": "text"}, {"key": "reprocess", "value": "0", "description": "Whether to reprocess an already processed import (1 = true, 0 = false)", "type": "text"}]}, "url": {"raw": "{{URL}}/import/{{import_id}}/process", "host": ["{{URL}}"], "path": ["import", "{{import_id}}", "process"], "variable": [{"key": "import_id", "value": "1", "description": "The ID of the import to process"}]}}, "response": []}, {"name": "Update Import", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"model\": \"products\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/imports/{{import_id}}", "host": ["{{URL}}"], "path": ["imports", "{{import_id}}"], "variable": [{"key": "import_id", "value": "1", "description": "The ID of the import"}]}}, "response": []}]}, {"name": "💳 Subscription", "description": "Subscription management endpoints for billing and organization plans, including internal subscription management and ASAAS integration", "item": [{"name": "🔗 ASAAS", "item": [{"name": "Check Organization Access", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/organization/1/access-check", "host": ["{{URL}}"], "path": ["asaas", "organization", "1", "access-check"]}}, "response": []}, {"name": "Create ASAAS Subscription", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"subscription_id\": 1,\n  \"customer\": \"cus_000005492485\",\n  \"billingType\": \"CREDIT_CARD\",\n  \"value\": 99.90,\n  \"nextDueDate\": \"2024-02-01\",\n  \"cycle\": \"MONTHLY\",\n  \"description\": \"Monthly subscription plan\",\n  \"endDate\": \"2024-12-31\",\n  \"maxPayments\": 12\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/asaas/subscription/create-asaas-subscription", "host": ["{{URL}}"], "path": ["asaas", "subscription", "create-asaas-subscription"]}}, "response": []}, {"name": "Create Client Customer", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"client_id\": 1,\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"cpfCnpj\": \"***********\",\n  \"phone\": \"***********\",\n  \"mobilePhone\": \"***********\",\n  \"address\": \"<PERSON>ua Example, 123\",\n  \"addressNumber\": \"123\",\n  \"complement\": \"Apt 1\",\n  \"province\": \"Centro\",\n  \"postalCode\": \"********\",\n  \"externalReference\": \"CLIENT_001\",\n  \"notificationDisabled\": false,\n  \"additionalEmails\": \"<EMAIL>\",\n  \"municipalInscription\": \"123456\",\n  \"stateInscription\": \"ISENTO\",\n  \"observations\": \"VIP Customer\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/asaas/client/create-customer", "host": ["{{URL}}"], "path": ["asaas", "client", "create-customer"]}}, "response": []}, {"name": "Create Organization Subaccount", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"organization_id\": 1,\n  \"name\": \"Organization Subaccount\",\n  \"email\": \"<EMAIL>\",\n  \"cpfCnpj\": \"***********\",\n  \"phone\": \"***********\",\n  \"mobilePhone\": \"***********\",\n  \"address\": \"<PERSON>ua Example, 123\",\n  \"addressNumber\": \"123\",\n  \"complement\": \"Sala 1\",\n  \"province\": \"Centro\",\n  \"postalCode\": \"********\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/asaas/organization/create-subaccount", "host": ["{{URL}}"], "path": ["asaas", "organization", "create-subaccount"]}}, "response": []}, {"name": "Create Sale Payment", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"sale_id\": 1,\n  \"customer\": \"cus_000005492485\",\n  \"billingType\": \"CREDIT_CARD\",\n  \"value\": 299.90,\n  \"dueDate\": \"2024-02-01\",\n  \"description\": \"Product sale payment\",\n  \"externalReference\": \"SALE_001\",\n  \"installmentCount\": 1,\n  \"installmentValue\": 299.90,\n  \"discount\": {\n    \"value\": 10.00,\n    \"dueDateLimitDays\": 5\n  },\n  \"interest\": {\n    \"value\": 2.00\n  },\n  \"fine\": {\n    \"value\": 1.00\n  },\n  \"postalService\": false\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/asaas/sale/create-payment", "host": ["{{URL}}"], "path": ["asaas", "sale", "create-payment"]}}, "response": []}, {"name": "Get Client Integration Status", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/client/1/status", "host": ["{{URL}}"], "path": ["asaas", "client", "1", "status"]}}, "response": []}, {"name": "Get Organization Integration Status", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/organization/1/status", "host": ["{{URL}}"], "path": ["asaas", "organization", "1", "status"]}}, "response": []}, {"name": "Get Sale Integration Status", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/sale/1/status", "host": ["{{URL}}"], "path": ["asaas", "sale", "1", "status"]}}, "response": []}, {"name": "Get Subscription Integration Status", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/subscription/1/status", "host": ["{{URL}}"], "path": ["asaas", "subscription", "1", "status"]}}, "response": []}, {"name": "Sync Subscription", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/subscription/sync-subscription/1", "host": ["{{URL}}"], "path": ["asaas", "subscription", "sync-subscription", "1"]}}, "response": []}, {"name": "🏦 Account", "item": [{"name": "Create Subaccount", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"organization_id\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/asaas/account/subaccount", "host": ["{{URL}}"], "path": ["asaas", "account", "subaccount"]}}, "response": []}, {"name": "Delete Subaccount", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/account/subaccount?organization_id=1", "host": ["{{URL}}"], "path": ["asaas", "account", "subaccount"], "query": [{"key": "organization_id", "value": "1"}]}}, "response": []}, {"name": "Get Balance", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/account/balance", "host": ["{{URL}}"], "path": ["asaas", "account", "balance"]}}, "response": []}, {"name": "Get My Account", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/account/my-account", "host": ["{{URL}}"], "path": ["asaas", "account", "my-account"]}}, "response": []}, {"name": "Get Statistics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/account/statistics?dateFrom=2024-01-01&dateTo=2024-12-31", "host": ["{{URL}}"], "path": ["asaas", "account", "statistics"], "query": [{"key": "dateFrom", "value": "2024-01-01"}, {"key": "dateTo", "value": "2024-12-31"}]}}, "response": []}, {"name": "Get Subaccount", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/account/subaccount?organization_id=1", "host": ["{{URL}}"], "path": ["asaas", "account", "subaccount"], "query": [{"key": "organization_id", "value": "1"}]}}, "response": []}, {"name": "Get Subaccounts", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/account/subaccounts?limit=20&offset=0", "host": ["{{URL}}"], "path": ["asaas", "account", "subaccounts"], "query": [{"key": "limit", "value": "20"}, {"key": "offset", "value": "0"}]}}, "response": []}, {"name": "Search Subaccount", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/account/subaccount/search?organization_id=1", "host": ["{{URL}}"], "path": ["asaas", "account", "subaccount", "search"], "query": [{"key": "organization_id", "value": "1", "description": "Organization ID to search for"}]}}, "response": []}, {"name": "Sync Organization Subaccount", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/account/organization/sync?organization_id=1", "host": ["{{URL}}"], "path": ["asaas", "account", "organization", "sync"], "query": [{"key": "organization_id", "value": "1", "description": "Organization ID to search for"}]}}, "response": []}, {"name": "Update My Account", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Company Name\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"***********\",\n  \"mobilePhone\": \"***********\",\n  \"address\": \"Updated Address\",\n  \"addressNumber\": \"123\",\n  \"complement\": \"Suite 456\",\n  \"province\": \"Updated District\",\n  \"postalCode\": \"12345-678\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/asaas/account/my-account", "host": ["{{URL}}"], "path": ["asaas", "account", "my-account"]}}, "response": []}, {"name": "Update Subaccount", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"organization_id\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/asaas/account/subaccount", "host": ["{{URL}}"], "path": ["asaas", "account", "subaccount"]}}, "response": []}]}, {"name": "👥 Customer", "item": [{"name": "Create Customer", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"***********\",\n  \"mobilePhone\": \"***********\",\n  \"cpfCnpj\": \"***********\",\n  \"personType\": \"FISICA\",\n  \"address\": \"Rua das Flores\",\n  \"addressNumber\": \"123\",\n  \"complement\": \"Apto 45\",\n  \"province\": \"Centro\",\n  \"city\": \"São Paulo\",\n  \"state\": \"SP\",\n  \"postalCode\": \"01234-567\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/asaas/customer", "host": ["{{URL}}"], "path": ["asaas", "customer"]}}, "response": []}, {"name": "Delete Customer", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/customer/cus_000005492182", "host": ["{{URL}}"], "path": ["asaas", "customer", "cus_000005492182"]}}, "response": []}, {"name": "Get All Customers", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/customers?limit=20&offset=0", "host": ["{{URL}}"], "path": ["asaas", "customers"], "query": [{"key": "limit", "value": "20"}, {"key": "offset", "value": "0"}]}}, "response": []}, {"name": "Get Customer by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/customer/cus_000005492182", "host": ["{{URL}}"], "path": ["asaas", "customer", "cus_000005492182"]}}, "response": []}, {"name": "Get Customer Notifications", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/customer/cus_000005492182/notifications?limit=20&offset=0", "host": ["{{URL}}"], "path": ["asaas", "customer", "cus_000005492182", "notifications"], "query": [{"key": "limit", "value": "20"}, {"key": "offset", "value": "0"}]}}, "response": []}, {"name": "Search Customer by Document", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/customer/search/document?cpf_cnpj=***********", "host": ["{{URL}}"], "path": ["asaas", "customer", "search", "document"], "query": [{"key": "cpf_cnpj", "value": "***********"}]}}, "response": []}, {"name": "Search Customer by Email", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/customer/search/email?email=<EMAIL>", "host": ["{{URL}}"], "path": ["asaas", "customer", "search", "email"], "query": [{"key": "email", "value": "<EMAIL>"}]}}, "response": []}, {"name": "Update Customer", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"***********\",\n  \"mobilePhone\": \"***********\",\n  \"address\": \"Rua das Flores Updated\",\n  \"addressNumber\": \"456\",\n  \"complement\": \"Apto 78\",\n  \"province\": \"Centro\",\n  \"city\": \"São Paulo\",\n  \"state\": \"SP\",\n  \"postalCode\": \"01234-567\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/asaas/customer/cus_000005492182", "host": ["{{URL}}"], "path": ["asaas", "customer", "cus_000005492182"]}}, "response": []}]}, {"name": "💰 Payment", "item": [{"name": "Create Payment", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"customer\": \"cus_000005492182\",\n  \"billingType\": \"BOLETO\",\n  \"value\": 100.00,\n  \"dueDate\": \"2024-12-31\",\n  \"description\": \"Pagamento de teste\",\n  \"externalReference\": \"REF123456\",\n  \"installmentCount\": 1,\n  \"installmentValue\": 100.00\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/asaas/payment", "host": ["{{URL}}"], "path": ["asaas", "payment"]}}, "response": []}, {"name": "Delete Payment", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/payment/pay_*********", "host": ["{{URL}}"], "path": ["asaas", "payment", "pay_*********"]}}, "response": []}, {"name": "Get All Payments", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/payments?limit=20&offset=0&customer=cus_000005492182", "host": ["{{URL}}"], "path": ["asaas", "payments"], "query": [{"key": "limit", "value": "20"}, {"key": "offset", "value": "0"}, {"key": "customer", "value": "cus_000005492182"}]}}, "response": []}, {"name": "Get Payment by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/payment/pay_*********", "host": ["{{URL}}"], "path": ["asaas", "payment", "pay_*********"]}}, "response": []}, {"name": "Update Payment", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"value\": 150.00,\n  \"dueDate\": \"2024-12-31\",\n  \"description\": \"Pagamento de teste atualizado\",\n  \"externalReference\": \"REF123456-UPDATED\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/asaas/payment/pay_*********", "host": ["{{URL}}"], "path": ["asaas", "payment", "pay_*********"]}}, "response": []}]}, {"name": "Resources", "item": [{"name": "🏪 Clients", "item": [{"name": "Create ASAAS Client", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"client_id\": 1,\n    \"organization_id\": 1,\n    \"asaas_customer_id\": \"cus_000005492183\",\n    \"sync_status\": \"synced\",\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"11912345678\",\n    \"mobile_phone\": \"11987654321\",\n    \"address\": \"Rua das Palmeiras, 456\",\n    \"address_number\": \"456\",\n    \"complement\": \"Apto 789\",\n    \"province\": \"Vila Madalena\",\n    \"city_name\": \"São Paulo\",\n    \"state\": \"SP\",\n    \"country\": \"Brasil\",\n    \"postal_code\": \"05435-010\",\n    \"cpf_cnpj\": \"123.456.789-00\",\n    \"person_type\": \"FISICA\",\n    \"external_reference\": \"CLIENT_001\",\n    \"notification_disabled\": false,\n    \"additional_emails\": \"<EMAIL>\",\n    \"observations\": \"Cliente pessoa física com histórico de pagamentos em dia\",\n    \"foreign_customer\": false\n}"}, "url": {"raw": "{{URL}}/asaas-resources/clients", "host": ["{{URL}}"], "path": ["asaas-resources", "clients"]}, "description": "Create a new ASAAS client record in the database"}, "response": []}, {"name": "Delete ASAAS Client", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "url": {"raw": "{{URL}}/asaas-resources/clients/1", "host": ["{{URL}}"], "path": ["asaas-resources", "clients", "1"]}, "description": "Soft delete an ASAAS client record"}, "response": []}, {"name": "Get ASAAS Client", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "url": {"raw": "{{URL}}/asaas-resources/clients/1", "host": ["{{URL}}"], "path": ["asaas-resources", "clients", "1"]}, "description": "Retrieve a specific ASAAS client by ID"}, "response": []}, {"name": "List ASAAS Clients", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "url": {"raw": "{{URL}}/asaas-resources/clients", "host": ["{{URL}}"], "path": ["asaas-resources", "clients"]}, "description": "Retrieve all ASAAS clients from the database"}, "response": []}, {"name": "Update ASAAS Client", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>ualiza<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"sync_status\": \"pending\",\n    \"mobile_phone\": \"11999887766\",\n    \"observations\": \"Cliente atualizado com novo telefone e email\"\n}"}, "url": {"raw": "{{URL}}/asaas-resources/clients/1", "host": ["{{URL}}"], "path": ["asaas-resources", "clients", "1"]}, "description": "Update an existing ASAAS client record"}, "response": []}]}, {"name": "Organization customers", "item": [{"name": "Create ASAAS Organization Customer", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"organization_id\": 1,\n    \"asaas_customer_id\": \"cus_000005492182\",\n    \"sync_status\": \"synced\",\n    \"name\": \"Cliente Organização Exemplo\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"11987654321\",\n    \"mobile_phone\": \"11976543210\",\n    \"address\": \"Av. Paul<PERSON>, 1000\",\n    \"address_number\": \"1000\",\n    \"complement\": \"Conjunto 101\",\n    \"province\": \"Bela Vista\",\n    \"city_name\": \"São Paulo\",\n    \"state\": \"SP\",\n    \"country\": \"Brasil\",\n    \"postal_code\": \"01310-100\",\n    \"cpf_cnpj\": \"98.765.432/0001-10\",\n    \"person_type\": \"JURIDICA\",\n    \"external_reference\": \"ORG_CUSTOMER_001\",\n    \"notification_disabled\": false,\n    \"additional_emails\": \"<EMAIL>,<EMAIL>\",\n    \"observations\": \"Cliente corporativo com alto volume de transações\",\n    \"foreign_customer\": false\n}"}, "url": {"raw": "{{URL}}/asaas-resources/organization-customers", "host": ["{{URL}}"], "path": ["asaas-resources", "organization-customers"]}, "description": "Create a new ASAAS organization customer record in the database"}, "response": []}, {"name": "Delete ASAAS Organization Customer", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "url": {"raw": "{{URL}}/asaas-resources/organization-customers/1", "host": ["{{URL}}"], "path": ["asaas-resources", "organization-customers", "1"]}, "description": "Soft delete an ASAAS organization customer record"}, "response": []}, {"name": "Get ASAAS Organization Customer", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "url": {"raw": "{{URL}}/asaas-resources/organization-customers/1", "host": ["{{URL}}"], "path": ["asaas-resources", "organization-customers", "1"]}, "description": "Retrieve a specific ASAAS organization customer by ID"}, "response": []}, {"name": "List ASAAS Organization Customers", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "url": {"raw": "{{URL}}/asaas-resources/organization-customers", "host": ["{{URL}}"], "path": ["asaas-resources", "organization-customers"]}, "description": "Retrieve all ASAAS organization customers from the database"}, "response": []}, {"name": "Update ASAAS Organization Customer", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Cliente Organização Exemplo - Atualizado\",\n    \"email\": \"<EMAIL>\",\n    \"sync_status\": \"pending\",\n    \"notification_disabled\": true,\n    \"observations\": \"Cliente atualizado com novas informações de contato\"\n}"}, "url": {"raw": "{{URL}}/asaas-resources/organization-customers/1", "host": ["{{URL}}"], "path": ["asaas-resources", "organization-customers", "1"]}, "description": "Update an existing ASAAS organization customer record"}, "response": []}]}, {"name": "🏢 Organizations", "item": [{"name": "Create ASAAS Organization", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"organization_id\": 1,\n    \"asaas_account_id\": \"acc_*********\",\n    \"asaas_api_key\": \"$aact_YTU5YTE0M2M2N2I4MTliNzk0YTI5N2U5MzdjNWZmNDQ6OjAwMDAwMDAwMDAwMDAwNzI2NzM6OiRhYWNoXzRlNTEzYWY3LWVmMTItNGE5Yy1iMDMyLTM5ZGRkOGVkNjE4OA==\",\n    \"asaas_wallet_id\": \"wallet_*********\",\n    \"asaas_environment\": \"sandbox\",\n    \"is_active\": true,\n    \"name\": \"Empresa Exemplo LTDA\",\n    \"email\": \"<EMAIL>\",\n    \"login_email\": \"<EMAIL>\",\n    \"phone\": \"***********\",\n    \"mobile_phone\": \"***********\",\n    \"address\": \"Rua das Flores, 123\",\n    \"address_number\": \"123\",\n    \"complement\": \"Sala 456\",\n    \"province\": \"Centro\",\n    \"postal_code\": \"01234-567\",\n    \"cpf_cnpj\": \"12.345.678/0001-90\",\n    \"person_type\": \"JURIDICA\",\n    \"company_type\": \"LTDA\",\n    \"city\": 3550308,\n    \"state\": \"SP\",\n    \"country\": \"Brasil\",\n    \"trading_name\": \"Empresa Exemplo\",\n    \"income_value\": 50000.00,\n    \"site\": \"https://www.empresaexemplo.com.br\"\n}"}, "url": {"raw": "{{URL}}/asaas-resources/organizations", "host": ["{{URL}}"], "path": ["asaas-resources", "organizations"]}, "description": "Create a new ASAAS organization record in the database"}, "response": []}, {"name": "Delete ASAAS Organization", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "url": {"raw": "{{URL}}/asaas-resources/organizations/1", "host": ["{{URL}}"], "path": ["asaas-resources", "organizations", "1"]}, "description": "Soft delete an ASAAS organization record"}, "response": []}, {"name": "Get ASAAS Organization", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "url": {"raw": "{{URL}}/asaas-resources/organizations/1", "host": ["{{URL}}"], "path": ["asaas-resources", "organizations", "1"]}, "description": "Retrieve a specific ASAAS organization by ID"}, "response": []}, {"name": "List ASAAS Organizations", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "url": {"raw": "{{URL}}/asaas-resources/organizations", "host": ["{{URL}}"], "path": ["asaas-resources", "organizations"]}, "description": "Retrieve all ASAAS organizations from the database"}, "response": []}, {"name": "Update ASAAS Organization", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Empresa Exemplo LTDA - Atualizada\",\n    \"email\": \"<EMAIL>\",\n    \"is_active\": false,\n    \"asaas_environment\": \"production\",\n    \"income_value\": 75000.00\n}"}, "url": {"raw": "{{URL}}/asaas-resources/organizations/1", "host": ["{{URL}}"], "path": ["asaas-resources", "organizations", "1"]}, "description": "Update an existing ASAAS organization record"}, "response": []}]}, {"name": "💰 Sales", "item": [{"name": "Create ASAAS Sale", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"sale_id\": 1,\n    \"organization_id\": 1,\n    \"client_id\": 1,\n    \"asaas_payment_id\": \"pay_*********\",\n    \"asaas_customer_id\": \"cus_000005492183\",\n    \"value\": 150.00,\n    \"net_value\": 145.50,\n    \"original_value\": 150.00,\n    \"interest_value\": 0.00,\n    \"discount_value\": 4.50,\n    \"description\": \"Pagamento de serviços de consultoria\",\n    \"billing_type\": \"PIX\",\n    \"due_date\": \"2024-12-31\",\n    \"status\": \"PENDING\",\n    \"external_reference\": \"SALE_001_2024\",\n    \"installment_count\": 1,\n    \"installment_value\": 150.00,\n    \"installment_number\": 1,\n    \"anticipated\": false,\n    \"anticipable\": true,\n    \"can_be_paid_after_due_date\": true,\n    \"sync_status\": \"synced\",\n    \"discount_config\": {\n        \"value\": 4.50,\n        \"dueDateLimitDays\": 0,\n        \"type\": \"FIXED\"\n    },\n    \"fine_config\": {\n        \"value\": 2.00,\n        \"type\": \"FIXED\"\n    },\n    \"interest_config\": {\n        \"value\": 1.00,\n        \"type\": \"PERCENTAGE\"\n    }\n}"}, "url": {"raw": "{{URL}}/asaas-resources/sales", "host": ["{{URL}}"], "path": ["asaas-resources", "sales"]}, "description": "Create a new ASAAS sale record in the database"}, "response": []}, {"name": "Delete ASAAS Sale", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "url": {"raw": "{{URL}}/asaas-resources/sales/1", "host": ["{{URL}}"], "path": ["asaas-resources", "sales", "1"]}, "description": "Soft delete an ASAAS sale record"}, "response": []}, {"name": "Get ASAAS Sale", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "url": {"raw": "{{URL}}/asaas-resources/sales/1", "host": ["{{URL}}"], "path": ["asaas-resources", "sales", "1"]}, "description": "Retrieve a specific ASAAS sale by ID"}, "response": []}, {"name": "List ASAAS Sales", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "url": {"raw": "{{URL}}/asaas-resources/sales", "host": ["{{URL}}"], "path": ["asaas-resources", "sales"]}, "description": "Retrieve all ASAAS sales from the database"}, "response": []}, {"name": "Update ASAAS Sale", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"RECEIVED\",\n    \"payment_date\": \"2024-08-18\",\n    \"client_payment_date\": \"2024-08-18\",\n    \"net_value\": 145.50,\n    \"sync_status\": \"synced\",\n    \"description\": \"Pagamento de serviços de consultoria - Recebido\"\n}"}, "url": {"raw": "{{URL}}/asaas-resources/sales/1", "host": ["{{URL}}"], "path": ["asaas-resources", "sales", "1"]}, "description": "Update an existing ASAAS sale record"}, "response": []}]}, {"name": "Subscriptions", "item": [{"name": "Create ASAAS Subscription", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"subscription_id\": 1,\n    \"asaas_customer_id\": \"cus_000005492183\",\n    \"asaas_subscription_id\": \"sub_*********\",\n    \"billing_type\": \"CREDIT_CARD\",\n    \"cycle\": \"MONTHLY\",\n    \"value\": 99.90,\n    \"next_due_date\": \"2024-09-18\",\n    \"description\": \"Assinatura mensal do plano premium\",\n    \"status\": \"ACTIVE\",\n    \"max_payments\": 12,\n    \"external_reference\": \"SUBSCRIPTION_001\",\n    \"payment_link\": \"https://www.asaas.com/c/*********\",\n    \"discount_value\": 9.99,\n    \"discount_type\": \"FIXED\",\n    \"discount_due_date_limit_days\": 5,\n    \"fine_value\": 5.00,\n    \"fine_type\": \"FIXED\",\n    \"interest_value\": 2.00,\n    \"credit_card_number\": \"**** **** **** 1234\",\n    \"credit_card_brand\": \"VISA\",\n    \"credit_card_token\": \"cc_token_*********\",\n    \"sync_status\": \"synced\",\n    \"split_data\": {\n        \"walletId\": \"wallet_*********\",\n        \"fixedValue\": 10.00,\n        \"percentualValue\": 5.0\n    }\n}"}, "url": {"raw": "{{URL}}/asaas-resources/subscriptions", "host": ["{{URL}}"], "path": ["asaas-resources", "subscriptions"]}, "description": "Create a new ASAAS subscription record in the database"}, "response": []}, {"name": "Delete ASAAS Subscription", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "url": {"raw": "{{URL}}/asaas-resources/subscriptions/1", "host": ["{{URL}}"], "path": ["asaas-resources", "subscriptions", "1"]}, "description": "Soft delete an ASAAS subscription record"}, "response": []}, {"name": "Get ASAAS Subscription", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "url": {"raw": "{{URL}}/asaas-resources/subscriptions/1", "host": ["{{URL}}"], "path": ["asaas-resources", "subscriptions", "1"]}, "description": "Retrieve a specific ASAAS subscription by ID"}, "response": []}, {"name": "List ASAAS Subscriptions", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "url": {"raw": "{{URL}}/asaas-resources/subscriptions", "host": ["{{URL}}"], "path": ["asaas-resources", "subscriptions"]}, "description": "Retrieve all ASAAS subscriptions from the database"}, "response": []}, {"name": "Update ASAAS Subscription", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"INACTIVE\",\n    \"end_date\": \"2024-12-31\",\n    \"value\": 89.90,\n    \"next_due_date\": \"2024-09-18\",\n    \"sync_status\": \"pending\",\n    \"description\": \"Assinatura mensal do plano premium - Cancelada\"\n}"}, "url": {"raw": "{{URL}}/asaas-resources/subscriptions/1", "host": ["{{URL}}"], "path": ["asaas-resources", "subscriptions", "1"]}, "description": "Update an existing ASAAS subscription record"}, "response": []}]}]}, {"name": "🔄 Subscription", "item": [{"name": "Create Subscription", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"customer\": \"cus_000005492182\",\n  \"billingType\": \"CREDIT_CARD\",\n  \"value\": 50.00,\n  \"nextDueDate\": \"2024-01-31\",\n  \"cycle\": \"MONTHLY\",\n  \"description\": \"Assinatura mensal\",\n  \"endDate\": \"2024-12-31\",\n  \"maxPayments\": 12\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/asaas/subscription", "host": ["{{URL}}"], "path": ["asaas", "subscription"]}}, "response": []}, {"name": "Delete Subscription", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/subscription/sub_*********", "host": ["{{URL}}"], "path": ["asaas", "subscription", "sub_*********"]}}, "response": []}, {"name": "Get All Subscriptions", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/subscriptions?limit=20&offset=0&customer=cus_000005492182", "host": ["{{URL}}"], "path": ["asaas", "subscriptions"], "query": [{"key": "limit", "value": "20"}, {"key": "offset", "value": "0"}, {"key": "customer", "value": "cus_000005492182"}]}}, "response": []}, {"name": "Get Subscription by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/subscription/sub_*********", "host": ["{{URL}}"], "path": ["asaas", "subscription", "sub_*********"]}}, "response": []}, {"name": "Get Subscription Payments", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/asaas/subscription/sub_*********/payments?limit=20&offset=0", "host": ["{{URL}}"], "path": ["asaas", "subscription", "sub_*********", "payments"], "query": [{"key": "limit", "value": "20"}, {"key": "offset", "value": "0"}]}}, "response": []}, {"name": "Update Subscription", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"value\": 75.00,\n  \"nextDueDate\": \"2024-02-28\",\n  \"description\": \"Assinatura mensal atualizada\",\n  \"endDate\": \"2024-12-31\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/asaas/subscription/sub_*********", "host": ["{{URL}}"], "path": ["asaas", "subscription", "sub_*********"]}}, "response": []}]}], "description": "ASAAS integration endpoints - organization subaccounts, subscription management, client customers, and payment processing"}, {"name": "🏢 Internal Subscription", "item": [{"name": "Create Subscription", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"organization_id\": 1,\n  \"plan_id\": 1,\n  \"start_date\": \"2024-01-01\",\n  \"end_date\": \"2024-12-31\",\n  \"status\": \"active\",\n  \"payment_method\": \"credit_card\",\n  \"auto_renew\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/subscriptions", "host": ["{{URL}}"], "path": ["subscriptions"]}}, "response": []}, {"name": "Get Subscription", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/subscriptions/1", "host": ["{{URL}}"], "path": ["subscriptions", "1"]}}, "response": []}, {"name": "Get Subscription by Organization", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/subscriptions/organization/1", "host": ["{{URL}}"], "path": ["subscriptions", "organization", "1"]}}, "response": []}, {"name": "<PERSON>", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"organization_id\": 1,\n  \"days\": 30,\n  \"reason\": \"Customer support courtesy extension\",\n  \"granted_by\": \"admin\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/subscriptions/grant-courtesy", "host": ["{{URL}}"], "path": ["subscriptions", "grant-courtesy"]}}, "response": []}, {"name": "Revoke Courtesy", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/subscriptions/revoke-courtesy/1", "host": ["{{URL}}"], "path": ["subscriptions", "revoke-courtesy", "1"]}}, "response": []}, {"name": "Update Subscription", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"plan_id\": 2,\n  \"end_date\": \"2025-12-31\",\n  \"status\": \"active\",\n  \"auto_renew\": false\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/subscriptions/1", "host": ["{{URL}}"], "path": ["subscriptions", "1"]}}, "response": []}], "description": "Internal subscription management - CRUD operations, courtesy management, and organization subscriptions"}]}, {"name": "📄 NFSe", "description": "⚠️ ENDPOINTS NOT IMPLEMENTED - Electronic Service Invoice (NFSe) management endpoints. No controllers or routes exist for NFSe management.", "item": [{"name": "Placeholder - Create NFSe", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"company_id\": 1,\n  \"client_id\": 1,\n  \"service_description\": \"Software development services\",\n  \"amount\": 1000.0,\n  \"tax_rate\": 5.0\n}"}, "url": {"raw": "{{URL}}/nfse/invoices", "host": ["{{URL}}"], "path": ["nfse", "invoices"]}}, "response": []}, {"name": "Placeholder - NFSe Companies", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/nfse/companies", "host": ["{{URL}}"], "path": ["nfse", "companies"]}}, "response": []}, {"name": "Placeholder - NFSe Invoices", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/nfse/invoices", "host": ["{{URL}}"], "path": ["nfse", "invoices"]}}, "response": []}, {"name": "Required Controllers", "request": {"method": "GET", "header": [], "url": {"raw": "# This is a documentation placeholder", "hash": " This is a documentation placeholder"}}, "response": []}]}, {"name": "📦 Inventory", "description": "Inventory management endpoints including products, brands, clients, projects, budgets, stocks, sales, items, shops, and batches", "item": [{"name": "📦 Batches", "item": [{"name": "Create Batch", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>ch Name\"\n}"}, "url": {"raw": "{{URL}}/batches", "host": ["{{URL}}"], "path": ["batches"]}}, "response": []}, {"name": "Delete Batch", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/batches/1", "host": ["{{URL}}"], "path": ["batches", "1"]}}, "response": []}, {"name": "Get All Batches", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/batches?order_by=created_at&order_direction=desc", "host": ["{{URL}}"], "path": ["batches"], "query": [{"key": "order_by", "value": "created_at", "description": "Order by field"}, {"key": "order_direction", "value": "desc", "description": "Order direction"}]}}, "response": []}, {"name": "<PERSON> Batch", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/batches/1", "host": ["{{URL}}"], "path": ["batches", "1"]}}, "response": []}, {"name": "Update Batch", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Batch Name\"\n}"}, "url": {"raw": "{{URL}}/batches/1", "host": ["{{URL}}"], "path": ["batches", "1"]}}, "response": []}], "description": "Batch management - CRUD operations with stock entry and exit tracking"}, {"name": "🏷️ Brands", "item": [{"name": "Create Brand", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Brand Name\",\n  \"description\": \"Brand description\"\n}"}, "url": {"raw": "{{URL}}/brands", "host": ["{{URL}}"], "path": ["brands"]}}, "response": []}, {"name": "Delete Brand", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/brands/1", "host": ["{{URL}}"], "path": ["brands", "1"]}}, "response": []}, {"name": "Get All Brands", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/brands?name=&order_by=name&order_direction=asc", "host": ["{{URL}}"], "path": ["brands"], "query": [{"key": "name", "value": "", "description": "Filter by brand name"}, {"key": "order_by", "value": "name", "description": "Order by field"}, {"key": "order_direction", "value": "asc", "description": "Order direction"}]}}, "response": []}, {"name": "Get Brand", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/brands/1", "host": ["{{URL}}"], "path": ["brands", "1"]}}, "response": []}, {"name": "Update Brand", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Brand Name\",\n  \"description\": \"Updated brand description\"\n}"}, "url": {"raw": "{{URL}}/brands/1", "host": ["{{URL}}"], "path": ["brands", "1"]}}, "response": []}], "description": "Brand management - CRUD operations"}, {"name": "💰 Budgets", "item": [{"name": "Create Budget", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"client_id\": 1,\n  \"project_id\": 1\n}"}, "url": {"raw": "{{URL}}/budgets", "host": ["{{URL}}"], "path": ["budgets"]}}, "response": []}, {"name": "Delete Budget", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/budgets/1", "host": ["{{URL}}"], "path": ["budgets", "1"]}}, "response": []}, {"name": "Get All Budgets", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/budgets?client=&client_id=&project_id=&order_by=created_at&order_direction=desc", "host": ["{{URL}}"], "path": ["budgets"], "query": [{"key": "client", "value": "", "description": "Filter by client name"}, {"key": "client_id", "value": "", "description": "Filter by client ID"}, {"key": "project_id", "value": "", "description": "Filter by project ID"}, {"key": "order_by", "value": "created_at", "description": "Order by field"}, {"key": "order_direction", "value": "desc", "description": "Order direction"}]}}, "response": []}, {"name": "Get Budget", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/budgets/1", "host": ["{{URL}}"], "path": ["budgets", "1"]}}, "response": []}, {"name": "Update Budget", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"client_id\": 2,\n  \"project_id\": 2\n}"}, "url": {"raw": "{{URL}}/budgets/1", "host": ["{{URL}}"], "path": ["budgets", "1"]}}, "response": []}], "description": "Budget management - CRUD operations with client and project filtering"}, {"name": "🏪 Clients", "item": [{"name": "Create Client", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Client Name\"\n}"}, "url": {"raw": "{{URL}}/clients", "host": ["{{URL}}"], "path": ["clients"]}}, "response": []}, {"name": "Delete Client", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/clients/1", "host": ["{{URL}}"], "path": ["clients", "1"]}}, "response": []}, {"name": "Get All Clients", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/clients?name=&order_by=name&order_direction=asc", "host": ["{{URL}}"], "path": ["clients"], "query": [{"key": "name", "value": "", "description": "Filter by client name"}, {"key": "order_by", "value": "name", "description": "Order by field"}, {"key": "order_direction", "value": "asc", "description": "Order direction"}]}}, "response": []}, {"name": "Get Client", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/clients/1", "host": ["{{URL}}"], "path": ["clients", "1"]}}, "response": []}, {"name": "Update Client", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Client Name\"\n}"}, "url": {"raw": "{{URL}}/clients/1", "host": ["{{URL}}"], "path": ["clients", "1"]}}, "response": []}], "description": "Client management - CRUD operations"}, {"name": "📋 Items", "item": [{"name": "Create <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": 1,\n  \"quantity\": 5,\n  \"price\": 100.0\n}"}, "url": {"raw": "{{URL}}/items", "host": ["{{URL}}"], "path": ["items"]}}, "response": []}, {"name": "Delete Item", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/items/1", "host": ["{{URL}}"], "path": ["items", "1"]}}, "response": []}, {"name": "Get All Items", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/items?product_id=&sale_id=&budget_id=&order_by=created_at&order_direction=desc", "host": ["{{URL}}"], "path": ["items"], "query": [{"key": "product_id", "value": "", "description": "Filter by product ID"}, {"key": "sale_id", "value": "", "description": "Filter by sale ID"}, {"key": "budget_id", "value": "", "description": "Filter by budget ID"}, {"key": "order_by", "value": "created_at", "description": "Order by field"}, {"key": "order_direction", "value": "desc", "description": "Order direction"}]}}, "response": []}, {"name": "Get Item", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/items/1", "host": ["{{URL}}"], "path": ["items", "1"]}}, "response": []}, {"name": "Update Item", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": 1,\n  \"quantity\": 10,\n  \"price\": 120.0\n}"}, "url": {"raw": "{{URL}}/items/1", "host": ["{{URL}}"], "path": ["items", "1"]}}, "response": []}], "description": "Item management - CRUD operations with product, sale, and budget filtering"}, {"name": "📦 Products", "item": [{"name": "Create Product", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Product Name\"\n}"}, "url": {"raw": "{{URL}}/products", "host": ["{{URL}}"], "path": ["products"]}}, "response": []}, {"name": "Delete Product", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/products/1", "host": ["{{URL}}"], "path": ["products", "1"]}}, "response": []}, {"name": "Get All Products", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/products?name=&brand_id=&has_stock=&order_by=name&order_direction=asc", "host": ["{{URL}}"], "path": ["products"], "query": [{"key": "name", "value": "", "description": "Filter by product name"}, {"key": "brand_id", "value": "", "description": "Filter by brand ID"}, {"key": "has_stock", "value": "", "description": "Filter by stock availability"}, {"key": "order_by", "value": "name", "description": "Order by field"}, {"key": "order_direction", "value": "asc", "description": "Order direction"}]}}, "response": []}, {"name": "Get Product", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/products/1", "host": ["{{URL}}"], "path": ["products", "1"]}}, "response": []}, {"name": "Update Product", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Product Name\"\n}"}, "url": {"raw": "{{URL}}/products/1", "host": ["{{URL}}"], "path": ["products", "1"]}}, "response": []}], "description": "Product management - CRUD operations with brand and stock filtering"}, {"name": "📋 Projects", "item": [{"name": "Create Project", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Project Name\"\n}"}, "url": {"raw": "{{URL}}/projects", "host": ["{{URL}}"], "path": ["projects"]}}, "response": []}, {"name": "Delete Project", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/projects/1", "host": ["{{URL}}"], "path": ["projects", "1"]}}, "response": []}, {"name": "Get All Projects", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/projects?name=&client=&client_id=&budget_id=&order_by=name&order_direction=asc", "host": ["{{URL}}"], "path": ["projects"], "query": [{"key": "name", "value": "", "description": "Filter by project name"}, {"key": "client", "value": "", "description": "Filter by client name"}, {"key": "client_id", "value": "", "description": "Filter by client ID"}, {"key": "budget_id", "value": "", "description": "Filter by budget ID"}, {"key": "order_by", "value": "name", "description": "Order by field"}, {"key": "order_direction", "value": "asc", "description": "Order direction"}]}}, "response": []}, {"name": "Get Project", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/projects/1", "host": ["{{URL}}"], "path": ["projects", "1"]}}, "response": []}, {"name": "Update Project", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Project Name\"\n}"}, "url": {"raw": "{{URL}}/projects/1", "host": ["{{URL}}"], "path": ["projects", "1"]}}, "response": []}], "description": "Project management - CRUD operations with client and budget filtering"}, {"name": "💰 Sales", "item": [{"name": "Create Sale", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"client_id\": 1,\n  \"project_id\": 1\n}"}, "url": {"raw": "{{URL}}/sales", "host": ["{{URL}}"], "path": ["sales"]}}, "response": []}, {"name": "Delete Sale", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/sales/1", "host": ["{{URL}}"], "path": ["sales", "1"]}}, "response": []}, {"name": "Get All Sales", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/sales?client=&client_id=&project_id=&order_by=created_at&order_direction=desc", "host": ["{{URL}}"], "path": ["sales"], "query": [{"key": "client", "value": "", "description": "Filter by client name"}, {"key": "client_id", "value": "", "description": "Filter by client ID"}, {"key": "project_id", "value": "", "description": "Filter by project ID"}, {"key": "order_by", "value": "created_at", "description": "Order by field"}, {"key": "order_direction", "value": "desc", "description": "Order direction"}]}}, "response": []}, {"name": "Get Sale", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/sales/1", "host": ["{{URL}}"], "path": ["sales", "1"]}}, "response": []}, {"name": "Update Sale", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"client_id\": 2,\n  \"project_id\": 2\n}"}, "url": {"raw": "{{URL}}/sales/1", "host": ["{{URL}}"], "path": ["sales", "1"]}}, "response": []}], "description": "Sales management - CRUD operations with client and project filtering"}, {"name": "🏪 Shops", "item": [{"name": "Create Shop", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Shop Name\"\n}"}, "url": {"raw": "{{URL}}/shops", "host": ["{{URL}}"], "path": ["shops"]}}, "response": []}, {"name": "Delete Shop", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/shops/1", "host": ["{{URL}}"], "path": ["shops", "1"]}}, "response": []}, {"name": "Get All Shops", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/shops?name=&order_by=name&order_direction=asc", "host": ["{{URL}}"], "path": ["shops"], "query": [{"key": "name", "value": "", "description": "Filter by shop name"}, {"key": "order_by", "value": "name", "description": "Order by field"}, {"key": "order_direction", "value": "asc", "description": "Order direction"}]}}, "response": []}, {"name": "Get Shop", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/shops/1", "host": ["{{URL}}"], "path": ["shops", "1"]}}, "response": []}, {"name": "Update Shop", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Shop Name\"\n}"}, "url": {"raw": "{{URL}}/shops/1", "host": ["{{URL}}"], "path": ["shops", "1"]}}, "response": []}], "description": "Shop management - CRUD operations"}, {"name": "📦 Stocks", "item": [{"name": "Create Stock", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": 1,\n  \"quantity\": 100\n}"}, "url": {"raw": "{{URL}}/stocks", "host": ["{{URL}}"], "path": ["stocks"]}}, "response": []}, {"name": "Delete Stock", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/stocks/1", "host": ["{{URL}}"], "path": ["stocks", "1"]}}, "response": []}, {"name": "Get All Stocks", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/stocks?product_id=&order_by=created_at&order_direction=desc", "host": ["{{URL}}"], "path": ["stocks"], "query": [{"key": "product_id", "value": "", "description": "Filter by product ID"}, {"key": "order_by", "value": "created_at", "description": "Order by field"}, {"key": "order_direction", "value": "desc", "description": "Order direction"}]}}, "response": []}, {"name": "Get Stock", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/stocks/1", "host": ["{{URL}}"], "path": ["stocks", "1"]}}, "response": []}, {"name": "Update Stock", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"product_id\": 1,\n  \"quantity\": 150\n}"}, "url": {"raw": "{{URL}}/stocks/1", "host": ["{{URL}}"], "path": ["stocks", "1"]}}, "response": []}], "description": "Stock management - CRUD operations with product filtering and stock entry/exit tracking"}]}, {"name": "🤖 ChatBot", "description": "ChatBot management endpoints including flows, steps, components, buttons, campaigns, messages, templates, parameters, interactions, and conversations", "item": [{"name": "📊 Analytics", "item": [{"name": "Get Campaign Analytics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/analytics/campaign/1?include_engagement=true&include_delivery=true", "host": ["{{URL}}"], "path": ["analytics", "campaign", "1"], "query": [{"key": "include_engagement", "value": "true", "description": "Include engagement metrics"}, {"key": "include_delivery", "value": "true", "description": "Include delivery metrics"}]}}, "response": []}, {"name": "Get Dashboard", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/analytics/dashboard?period=30d&timezone=America/Sao_Paulo", "host": ["{{URL}}"], "path": ["analytics", "dashboard"], "query": [{"key": "period", "value": "30d", "description": "Time period (7d, 30d, 90d, 1y)"}, {"key": "timezone", "value": "America/Sao_Paulo", "description": "Timezone for date calculations"}]}}, "response": []}, {"name": "Get Message Engagement Summary", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/analytics/message/1/engagement", "host": ["{{URL}}"], "path": ["analytics", "message", "1", "engagement"]}}, "response": []}, {"name": "Get Multiple Campaign Analytics", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"campaign_ids\": [1, 2, 3, 4, 5],\n  \"metrics\": [\"delivery_rate\", \"open_rate\", \"click_rate\", \"conversion_rate\"],\n  \"date_range\": {\n    \"start_date\": \"2024-01-01\",\n    \"end_date\": \"2024-01-31\"\n  },\n  \"group_by\": \"day\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/analytics/campaigns/multiple", "host": ["{{URL}}"], "path": ["analytics", "campaigns", "multiple"]}}, "response": []}, {"name": "Get Performance Comparison", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"campaign_ids\": [1, 2],\n  \"comparison_type\": \"side_by_side\",\n  \"metrics\": [\"delivery_rate\", \"engagement_rate\", \"conversion_rate\"],\n  \"date_range\": {\n    \"start_date\": \"2024-01-01\",\n    \"end_date\": \"2024-01-31\"\n  }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/analytics/campaigns/compare", "host": ["{{URL}}"], "path": ["analytics", "campaigns", "compare"]}}, "response": []}, {"name": "Record Bulk Engagement Events", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"events\": [\n    {\n      \"message_id\": 1,\n      \"event_type\": \"delivered\",\n      \"timestamp\": \"2024-01-15T10:00:00Z\",\n      \"client_id\": 1\n    },\n    {\n      \"message_id\": 1,\n      \"event_type\": \"read\",\n      \"timestamp\": \"2024-01-15T10:05:00Z\",\n      \"client_id\": 1\n    },\n    {\n      \"message_id\": 2,\n      \"event_type\": \"click\",\n      \"event_data\": {\n        \"button_id\": \"btn_2\"\n      },\n      \"timestamp\": \"2024-01-15T10:10:00Z\",\n      \"client_id\": 2\n    }\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/analytics/engagement/bulk", "host": ["{{URL}}"], "path": ["analytics", "engagement", "bulk"]}}, "response": []}, {"name": "Record Engagement Event", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"message_id\": 1,\n  \"event_type\": \"click\",\n  \"event_data\": {\n    \"button_id\": \"btn_1\",\n    \"button_text\": \"Learn More\",\n    \"timestamp\": \"2024-01-15T10:30:00Z\"\n  },\n  \"client_id\": 1,\n  \"campaign_id\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/analytics/engagement/record", "host": ["{{URL}}"], "path": ["analytics", "engagement", "record"]}}, "response": []}, {"name": "Trigger Calculation", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"calculation_type\": \"campaign_metrics\",\n  \"entity_ids\": [1, 2, 3],\n  \"force_recalculation\": false,\n  \"include_historical\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/analytics/trigger-calculation", "host": ["{{URL}}"], "path": ["analytics", "trigger-calculation"]}}, "response": []}], "description": "Analytics and metrics - campaign analytics, engagement tracking, performance comparison, and dashboard data"}, {"name": "🔘 Buttons", "item": [{"name": "C<PERSON> <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"reply\"\n}"}, "url": {"raw": "{{URL}}/buttons", "host": ["{{URL}}"], "path": ["buttons"]}}, "response": []}, {"name": "Delete Button", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/buttons/1", "host": ["{{URL}}"], "path": ["buttons", "1"]}}, "response": []}, {"name": "Get All Buttons", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/buttons?order_by=order&order_direction=asc", "host": ["{{URL}}"], "path": ["buttons"], "query": [{"key": "order_by", "value": "order", "description": "Order by field"}, {"key": "order_direction", "value": "asc", "description": "Order direction"}]}}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/buttons/1", "host": ["{{URL}}"], "path": ["buttons", "1"]}}, "response": []}, {"name": "Update <PERSON><PERSON>", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"quick_reply\"\n}"}, "url": {"raw": "{{URL}}/buttons/1", "host": ["{{URL}}"], "path": ["buttons", "1"]}}, "response": []}], "description": "Button management - interactive buttons for flows and templates"}, {"name": "📢 Campaigns", "item": [{"name": "Add Clients to Campaign", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"client_ids\": [\n    1,\n    2,\n    3\n  ]\n}"}, "url": {"raw": "{{URL}}/campaign/add-clients/1", "host": ["{{URL}}"], "path": ["campaign", "add-clients", "1"]}}, "response": []}, {"name": "Assign Categories", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"category_ids\": [1, 2, 3]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/campaign/1/categories", "host": ["{{URL}}"], "path": ["campaign", "1", "categories"]}}, "response": []}, {"name": "Assign <PERSON>s", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"tags\": [\"marketing\", \"promotion\", \"new-product\", \"seasonal\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/campaign/1/tags", "host": ["{{URL}}"], "path": ["campaign", "1", "tags"]}}, "response": []}, {"name": "Cancel Campaign", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Campaign no longer needed\",\n  \"cancel_pending_messages\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/campaign/1/cancel", "host": ["{{URL}}"], "path": ["campaign", "1", "cancel"]}}, "response": []}, {"name": "Create Campaign", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Welcome Campaign\"\n}"}, "url": {"raw": "{{URL}}/campaigns", "host": ["{{URL}}"], "path": ["campaigns"]}}, "response": []}, {"name": "Delete Campaign", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/campaigns/1", "host": ["{{URL}}"], "path": ["campaigns", "1"]}}, "response": []}, {"name": "Get All Campaigns", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/campaigns?name=&template_id=&phone_number_id=&sent_at_greater_than=&sent_at_lower_than=&scheduled_at_greater_than=&scheduled_at_lower_than=&is_sending=&is_sent=&is_scheduled=&order_by=name&order_direction=asc", "host": ["{{URL}}"], "path": ["campaigns"], "query": [{"key": "name", "value": "", "description": "Filter by campaign name"}, {"key": "template_id", "value": "", "description": "Filter by template ID"}, {"key": "phone_number_id", "value": "", "description": "Filter by phone number ID"}, {"key": "sent_at_greater_than", "value": "", "description": "Filter sent after date"}, {"key": "sent_at_lower_than", "value": "", "description": "Filter sent before date"}, {"key": "scheduled_at_greater_than", "value": "", "description": "Filter scheduled after date"}, {"key": "scheduled_at_lower_than", "value": "", "description": "Filter scheduled before date"}, {"key": "is_sending", "value": "", "description": "Filter by sending status"}, {"key": "is_sent", "value": "", "description": "Filter by sent status"}, {"key": "is_scheduled", "value": "", "description": "Filter by scheduled status"}, {"key": "order_by", "value": "name", "description": "Order by field"}, {"key": "order_direction", "value": "asc", "description": "Order direction"}]}}, "response": []}, {"name": "Get Campaign", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/campaigns/1", "host": ["{{URL}}"], "path": ["campaigns", "1"]}}, "response": []}, {"name": "Get Status History", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/campaign/1/status-history", "host": ["{{URL}}"], "path": ["campaign", "1", "status-history"]}}, "response": []}, {"name": "Get Status Timeline", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/campaign/1/status-timeline", "host": ["{{URL}}"], "path": ["campaign", "1", "status-timeline"]}}, "response": []}, {"name": "Remove <PERSON> from Campaign", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"client_id\": 1\n}"}, "url": {"raw": "{{URL}}/campaign/remove-client/1", "host": ["{{URL}}"], "path": ["campaign", "remove-client", "1"]}}, "response": []}, {"name": "Schedule Campaign", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"scheduled_at\": \"2024-12-31 10:00:00\"\n}"}, "url": {"raw": "{{URL}}/campaign/schedule/1", "host": ["{{URL}}"], "path": ["campaign", "schedule", "1"]}}, "response": []}, {"name": "Send Campaign", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/campaign/send/1", "host": ["{{URL}}"], "path": ["campaign", "send", "1"]}}, "response": []}, {"name": "Update Campaign", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Welcome Campaign\"\n}"}, "url": {"raw": "{{URL}}/campaigns/1", "host": ["{{URL}}"], "path": ["campaigns", "1"]}}, "response": []}], "description": "Campaign management - WhatsApp marketing campaigns and bulk messaging"}, {"name": "📂 Categories", "item": [{"name": "Create Category", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Marketing\",\n  \"description\": \"Marketing campaigns and promotional messages\",\n  \"color\": \"#3B82F6\",\n  \"is_active\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/categories", "host": ["{{URL}}"], "path": ["categories"]}}, "response": []}, {"name": "Delete Category", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/categories/1", "host": ["{{URL}}"], "path": ["categories", "1"]}}, "response": []}, {"name": "Get All Categories", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/categories?name=&order_by=name&order_direction=asc", "host": ["{{URL}}"], "path": ["categories"], "query": [{"key": "name", "value": "", "description": "Filter by category name"}, {"key": "order_by", "value": "name", "description": "Order by field"}, {"key": "order_direction", "value": "asc", "description": "Order direction"}]}}, "response": []}, {"name": "Get Category", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/categories/1", "host": ["{{URL}}"], "path": ["categories", "1"]}}, "response": []}, {"name": "Update Category", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Marketing\",\n  \"description\": \"Updated marketing campaigns and promotional messages\",\n  \"color\": \"#10B981\",\n  \"is_active\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/categories/1", "host": ["{{URL}}"], "path": ["categories", "1"]}}, "response": []}], "description": "Category management - CRUD operations for campaign categorization"}, {"name": "🧩 Components", "item": [{"name": "Create Component", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Welcome Component\"\n}"}, "url": {"raw": "{{URL}}/components", "host": ["{{URL}}"], "path": ["components"]}}, "response": []}, {"name": "Delete Component", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/components/1", "host": ["{{URL}}"], "path": ["components", "1"]}}, "response": []}, {"name": "Get All Components", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/components?name=&template_id=&format=&order_by=name&order_direction=asc", "host": ["{{URL}}"], "path": ["components"], "query": [{"key": "name", "value": "", "description": "Filter by component name"}, {"key": "template_id", "value": "", "description": "Filter by template ID"}, {"key": "format", "value": "", "description": "Filter by format (TEXT, IMAGE, VIDEO, DOCUMENT)"}, {"key": "order_by", "value": "name", "description": "Order by field"}, {"key": "order_direction", "value": "asc", "description": "Order direction"}]}}, "response": []}, {"name": "Get Component", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/components/1", "host": ["{{URL}}"], "path": ["components", "1"]}}, "response": []}, {"name": "Get Component WhatsApp Payload", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/component/get-to-whatsapp-payload/1", "host": ["{{URL}}"], "path": ["component", "get-to-whatsapp-payload", "1"]}}, "response": []}, {"name": "Update Component", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Welcome Component\"\n}"}, "url": {"raw": "{{URL}}/components/1", "host": ["{{URL}}"], "path": ["components", "1"]}}, "response": []}], "description": "Component management - message components and templates"}, {"name": "💭 Conversations", "item": [{"name": "Create Conversation", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"phone_number_id\": 1,\n  \"client_id\": 1,\n  \"flow_id\": 1\n}"}, "url": {"raw": "{{URL}}/conversations", "host": ["{{URL}}"], "path": ["conversations"]}}, "response": []}, {"name": "Delete Conversation", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/conversations/1", "host": ["{{URL}}"], "path": ["conversations", "1"]}}, "response": []}, {"name": "Get All Conversations", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/conversations?phone_number_id=&client_id=&flow_id=&is_active=&order_by=created_at&order_direction=desc", "host": ["{{URL}}"], "path": ["conversations"], "query": [{"key": "phone_number_id", "value": "", "description": "Filter by phone number ID"}, {"key": "client_id", "value": "", "description": "Filter by client ID"}, {"key": "flow_id", "value": "", "description": "Filter by flow ID"}, {"key": "is_active", "value": "", "description": "Filter by active status"}, {"key": "order_by", "value": "created_at", "description": "Order by field"}, {"key": "order_direction", "value": "desc", "description": "Order direction"}]}}, "response": []}, {"name": "Get Conversation", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/conversations/1", "host": ["{{URL}}"], "path": ["conversations", "1"]}}, "response": []}, {"name": "Update Conversation", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"phone_number_id\": 1,\n  \"client_id\": 2,\n  \"flow_id\": 1,\n  \"is_active\": false\n}"}, "url": {"raw": "{{URL}}/conversations/1", "host": ["{{URL}}"], "path": ["conversations", "1"]}}, "response": []}], "description": "Conversation management - chat conversations and session tracking"}, {"name": "📊 Flows", "item": [{"name": "Create Flow", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Welcome Flow\"\n}"}, "url": {"raw": "{{URL}}/flows", "host": ["{{URL}}"], "path": ["flows"]}}, "response": []}, {"name": "Delete Flow", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/flows/1", "host": ["{{URL}}"], "path": ["flows", "1"]}}, "response": []}, {"name": "Get All Flows", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/flows?name=&order_by=name&order_direction=asc", "host": ["{{URL}}"], "path": ["flows"], "query": [{"key": "name", "value": "", "description": "Filter by flow name"}, {"key": "order_by", "value": "name", "description": "Order by field"}, {"key": "order_direction", "value": "asc", "description": "Order direction"}]}}, "response": []}, {"name": "Get Flow", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/flows/1", "host": ["{{URL}}"], "path": ["flows", "1"]}}, "response": []}, {"name": "Save Full Flow", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"flow\": {\n    \"name\": \"Complete Flow\",\n    \"steps\": [\n      {\n        \"step\": \"Welcome\",\n        \"is_input\": false,\n        \"components\": [\n          {\n            \"name\": \"Welcome Message\",\n            \"type\": \"BODY\",\n            \"text\": \"Welcome to our service!\"\n          }\n        ]\n      }\n    ]\n  }\n}"}, "url": {"raw": "{{URL}}/flow/save", "host": ["{{URL}}"], "path": ["flow", "save"]}}, "response": []}, {"name": "Update Flow", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Welcome Flow\"\n}"}, "url": {"raw": "{{URL}}/flows/1", "host": ["{{URL}}"], "path": ["flows", "1"]}}, "response": []}], "description": "Flow management - conversation flows and automation sequences"}, {"name": "🔄 Interactions", "item": [{"name": "Create Interaction", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"conversation_id\": 1,\n  \"step_id\": 1,\n  \"input\": \"User response\"\n}"}, "url": {"raw": "{{URL}}/interactions", "host": ["{{URL}}"], "path": ["interactions"]}}, "response": []}, {"name": "Delete Interaction", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/interactions/1", "host": ["{{URL}}"], "path": ["interactions", "1"]}}, "response": []}, {"name": "Get All Interactions", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/interactions?conversation_id=&step_id=&order_by=created_at&order_direction=desc", "host": ["{{URL}}"], "path": ["interactions"], "query": [{"key": "conversation_id", "value": "", "description": "Filter by conversation ID"}, {"key": "step_id", "value": "", "description": "Filter by step ID"}, {"key": "order_by", "value": "created_at", "description": "Order by field"}, {"key": "order_direction", "value": "desc", "description": "Order direction"}]}}, "response": []}, {"name": "Get Interaction", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/interactions/1", "host": ["{{URL}}"], "path": ["interactions", "1"]}}, "response": []}, {"name": "Update Interaction", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"conversation_id\": 1,\n  \"step_id\": 2,\n  \"input\": \"Updated user response\"\n}"}, "url": {"raw": "{{URL}}/interactions/1", "host": ["{{URL}}"], "path": ["interactions", "1"]}}, "response": []}], "description": "Interaction management - user interactions within conversations and flows"}, {"name": "💬 Messages", "item": [{"name": "Create Message", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"campaign_id\": 1,\n  \"client_id\": 1\n}"}, "url": {"raw": "{{URL}}/messages", "host": ["{{URL}}"], "path": ["messages"]}}, "response": []}, {"name": "Delete Message", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/messages/1", "host": ["{{URL}}"], "path": ["messages", "1"]}}, "response": []}, {"name": "Get All Messages", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/messages?campaign_id=&client_id=&phone_number_id=&sent_at_greater_than=&sent_at_lower_than=&is_sent=&order_by=created_at&order_direction=desc", "host": ["{{URL}}"], "path": ["messages"], "query": [{"key": "campaign_id", "value": "", "description": "Filter by campaign ID"}, {"key": "client_id", "value": "", "description": "Filter by client ID"}, {"key": "phone_number_id", "value": "", "description": "Filter by phone number ID"}, {"key": "sent_at_greater_than", "value": "", "description": "Filter sent after date"}, {"key": "sent_at_lower_than", "value": "", "description": "Filter sent before date"}, {"key": "is_sent", "value": "", "description": "Filter by sent status"}, {"key": "order_by", "value": "created_at", "description": "Order by field"}, {"key": "order_direction", "value": "desc", "description": "Order direction"}]}}, "response": []}, {"name": "Get Campaign Statistics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/campaign/1/messages/statistics", "host": ["{{URL}}"], "path": ["campaign", "1", "messages", "statistics"]}}, "response": []}, {"name": "Get Delivery Status", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/message/1/delivery-status", "host": ["{{URL}}"], "path": ["message", "1", "delivery-status"]}}, "response": []}, {"name": "Get Failed Messages by Campaign", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/campaign/1/messages/failed?page=1&per_page=50", "host": ["{{URL}}"], "path": ["campaign", "1", "messages", "failed"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "per_page", "value": "50", "description": "Items per page"}]}}, "response": []}, {"name": "Get Message", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/messages/1", "host": ["{{URL}}"], "path": ["messages", "1"]}}, "response": []}, {"name": "Get Messages by Campaign", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/campaign/1/messages?status=&page=1&per_page=50", "host": ["{{URL}}"], "path": ["campaign", "1", "messages"], "query": [{"key": "status", "value": "", "description": "Filter by message status"}, {"key": "page", "value": "1", "description": "Page number"}, {"key": "per_page", "value": "50", "description": "Items per page"}]}}, "response": []}, {"name": "Resend Failed Messages by Campaign", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"max_retry_attempts\": 3,\n  \"delay_between_retries\": 300,\n  \"filter_by_error_type\": \"timeout\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/campaign/1/messages/resend-failed", "host": ["{{URL}}"], "path": ["campaign", "1", "messages", "resend-failed"]}}, "response": []}, {"name": "Resend Message", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/message/1/resend", "host": ["{{URL}}"], "path": ["message", "1", "resend"]}}, "response": []}, {"name": "Send Message", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/message/send/1", "host": ["{{URL}}"], "path": ["message", "send", "1"]}}, "response": []}, {"name": "Update Message", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"campaign_id\": 1,\n  \"client_id\": 2\n}"}, "url": {"raw": "{{URL}}/messages/1", "host": ["{{URL}}"], "path": ["messages", "1"]}}, "response": []}], "description": "Message management - individual messages within campaigns and conversations"}, {"name": "⚙️ Parameters", "item": [{"name": "Create Parameter", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"client_name\",\n  \"type\": \"TEXT\",\n  \"example\": \"<PERSON>\"\n}"}, "url": {"raw": "{{URL}}/parameters", "host": ["{{URL}}"], "path": ["parameters"]}}, "response": []}, {"name": "Delete Parameter", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/parameters/1", "host": ["{{URL}}"], "path": ["parameters", "1"]}}, "response": []}, {"name": "Get All Parameters", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/parameters?name=&type=&component_id=&order_by=name&order_direction=asc", "host": ["{{URL}}"], "path": ["parameters"], "query": [{"key": "name", "value": "", "description": "Filter by parameter name"}, {"key": "type", "value": "", "description": "Filter by parameter type"}, {"key": "component_id", "value": "", "description": "Filter by component ID"}, {"key": "order_by", "value": "name", "description": "Order by field"}, {"key": "order_direction", "value": "asc", "description": "Order direction"}]}}, "response": []}, {"name": "Get Parameter", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/parameters/1", "host": ["{{URL}}"], "path": ["parameters", "1"]}}, "response": []}, {"name": "Update Parameter", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"updated_client_name\",\n  \"type\": \"TEXT\",\n  \"example\": \"<PERSON>\"\n}"}, "url": {"raw": "{{URL}}/parameters/1", "host": ["{{URL}}"], "path": ["parameters", "1"]}}, "response": []}], "description": "Parameter management - dynamic parameters for templates and components"}, {"name": "🔗 Steps", "item": [{"name": "Create Step", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"step\": \"Welcome Step\",\n  \"is_input\": false,\n  \"input\": null\n}"}, "url": {"raw": "{{URL}}/steps", "host": ["{{URL}}"], "path": ["steps"]}}, "response": []}, {"name": "Delete Step", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/steps/1", "host": ["{{URL}}"], "path": ["steps", "1"]}}, "response": []}, {"name": "Get All Steps", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/steps?step=&is_input=&input=&order_by=order&order_direction=asc", "host": ["{{URL}}"], "path": ["steps"], "query": [{"key": "step", "value": "", "description": "Filter by step name"}, {"key": "is_input", "value": "", "description": "Filter by input type (true/false)"}, {"key": "input", "value": "", "description": "Filter by input content"}, {"key": "order_by", "value": "order", "description": "Order by field"}, {"key": "order_direction", "value": "asc", "description": "Order direction"}]}}, "response": []}, {"name": "Get Step", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/steps/1", "host": ["{{URL}}"], "path": ["steps", "1"]}}, "response": []}, {"name": "Update Step", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"step\": \"Updated Welcome Step\",\n  \"is_input\": true,\n  \"input\": \"Please enter your name\"\n}"}, "url": {"raw": "{{URL}}/steps/1", "host": ["{{URL}}"], "path": ["steps", "1"]}}, "response": []}], "description": "Step management - individual steps within flows"}, {"name": "🏷️ Tags", "item": [{"name": "Get All Tags", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/tags?search=&limit=50", "host": ["{{URL}}"], "path": ["tags"], "query": [{"key": "search", "value": "", "description": "Search tags by name"}, {"key": "limit", "value": "50", "description": "Limit number of results"}]}}, "response": []}, {"name": "Get Most Used Tags", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/tags/most-used?limit=20", "host": ["{{URL}}"], "path": ["tags", "most-used"], "query": [{"key": "limit", "value": "20", "description": "Limit number of most used tags"}]}}, "response": []}, {"name": "Get Tag Suggestions", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/tags/suggestions?query=mark&limit=10", "host": ["{{URL}}"], "path": ["tags", "suggestions"], "query": [{"key": "query", "value": "mark", "description": "Search query for tag suggestions"}, {"key": "limit", "value": "10", "description": "Limit number of suggestions"}]}}, "response": []}], "description": "Tag management - listing, most used tags, and suggestions for campaign tagging"}, {"name": "📄 Templates", "item": [{"name": "Create Template", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"welcome_template\",\n  \"category\": \"MARKETING\",\n  \"language\": \"en_US\"\n}"}, "url": {"raw": "{{URL}}/templates", "host": ["{{URL}}"], "path": ["templates"]}}, "response": []}, {"name": "Delete Template", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/templates/1", "host": ["{{URL}}"], "path": ["templates", "1"]}}, "response": []}, {"name": "Get All Templates", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/templates?name=&category=&language=&status=&order_by=name&order_direction=asc", "host": ["{{URL}}"], "path": ["templates"], "query": [{"key": "name", "value": "", "description": "Filter by template name"}, {"key": "category", "value": "", "description": "Filter by category"}, {"key": "language", "value": "", "description": "Filter by language"}, {"key": "status", "value": "", "description": "Filter by status"}, {"key": "order_by", "value": "name", "description": "Order by field"}, {"key": "order_direction", "value": "asc", "description": "Order direction"}]}}, "response": []}, {"name": "Get Template", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/templates/1", "host": ["{{URL}}"], "path": ["templates", "1"]}}, "response": []}, {"name": "Save Full Template", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"template\": {\n    \"name\": \"complete_template\",\n    \"category\": \"MARKETING\",\n    \"language\": \"en_US\",\n    \"components\": [\n      {\n        \"name\": \"Header\",\n        \"type\": \"HEADER\",\n        \"format\": \"TEXT\",\n        \"text\": \"Welcome!\"\n      },\n      {\n        \"name\": \"Body\",\n        \"type\": \"BODY\",\n        \"text\": \"Hello {{1}}, welcome to our service!\",\n        \"parameters\": [\n          {\n            \"name\": \"client_name\",\n            \"type\": \"TEXT\",\n            \"example\": \"<PERSON>\"\n          }\n        ]\n      }\n    ],\n    \"buttons\": [\n      {\n        \"type\": \"QUICK_REPLY\",\n        \"text\": \"Get Started\"\n      }\n    ]\n  }\n}"}, "url": {"raw": "{{URL}}/template/save", "host": ["{{URL}}"], "path": ["template", "save"]}}, "response": []}, {"name": "Update Template", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"updated_welcome_template\",\n  \"category\": \"UTILITY\",\n  \"language\": \"pt_BR\"\n}"}, "url": {"raw": "{{URL}}/templates/1", "host": ["{{URL}}"], "path": ["templates", "1"]}}, "response": []}], "description": "Template management - WhatsApp message templates and structures"}, {"name": "🔄 WhatsApp Sync", "item": [{"name": "Get Entity Logs", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/whatsapp/sync/entity-logs?entity_type=message&entity_id=1&include_details=true", "host": ["{{URL}}"], "path": ["whatsapp", "sync", "entity-logs"], "query": [{"key": "entity_type", "value": "message", "description": "Entity type (message, campaign)"}, {"key": "entity_id", "value": "1", "description": "Entity ID"}, {"key": "include_details", "value": "true", "description": "Include detailed sync information"}]}}, "response": []}, {"name": "Get Status Overview", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/whatsapp/sync/status-overview", "host": ["{{URL}}"], "path": ["whatsapp", "sync", "status-overview"]}}, "response": []}, {"name": "Get Sync Logs", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/whatsapp/sync/logs?entity_type=&entity_id=&status=&date_from=&date_to=&page=1&per_page=50", "host": ["{{URL}}"], "path": ["whatsapp", "sync", "logs"], "query": [{"key": "entity_type", "value": "", "description": "Filter by entity type (message, campaign)"}, {"key": "entity_id", "value": "", "description": "Filter by entity ID"}, {"key": "status", "value": "", "description": "Filter by sync status"}, {"key": "date_from", "value": "", "description": "Filter from date (YYYY-MM-DD)"}, {"key": "date_to", "value": "", "description": "Filter to date (YYYY-MM-DD)"}, {"key": "page", "value": "1", "description": "Page number"}, {"key": "per_page", "value": "50", "description": "Items per page"}]}}, "response": []}, {"name": "Get Sync Trends", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "url": {"raw": "{{URL}}/whatsapp/sync/trends?period=7d&group_by=day&include_success_rate=true", "host": ["{{URL}}"], "path": ["whatsapp", "sync", "trends"], "query": [{"key": "period", "value": "7d", "description": "Time period (1d, 7d, 30d, 90d)"}, {"key": "group_by", "value": "day", "description": "Group by (hour, day, week)"}, {"key": "include_success_rate", "value": "true", "description": "Include success rate metrics"}]}}, "response": []}, {"name": "Sync Campaign", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"sync_all_messages\": true,\n  \"force_sync\": false,\n  \"include_analytics\": true,\n  \"batch_size\": 100\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/whatsapp/sync/campaign/1", "host": ["{{URL}}"], "path": ["whatsapp", "sync", "campaign", "1"]}}, "response": []}, {"name": "Sync Message", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"force_sync\": false,\n  \"include_delivery_status\": true,\n  \"sync_engagement_data\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/whatsapp/sync/message/1", "host": ["{{URL}}"], "path": ["whatsapp", "sync", "message", "1"]}}, "response": []}, {"name": "<PERSON><PERSON> Proactive Sync", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{TOKEN}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"sync_type\": \"full\",\n  \"entity_types\": [\"message\", \"campaign\"],\n  \"priority\": \"high\",\n  \"batch_size\": 50,\n  \"delay_between_batches\": 1000\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/whatsapp/sync/trigger-proactive", "host": ["{{URL}}"], "path": ["whatsapp", "sync", "trigger-proactive"]}}, "response": []}], "description": "WhatsApp synchronization endpoints - message sync, campaign sync, logs, trends, and proactive sync triggers"}]}]}