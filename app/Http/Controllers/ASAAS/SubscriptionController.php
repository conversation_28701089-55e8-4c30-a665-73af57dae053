<?php

namespace App\Http\Controllers\ASAAS;

use App\Http\Controllers\Controller;
use App\Helpers\Traits\Response;
use App\Services\ASAAS\UseCases\Subscription\CreateSubscription;
use App\Services\ASAAS\UseCases\Subscription\GetAllSubscriptions;
use App\Services\ASAAS\UseCases\Subscription\GetSubscriptionById;
use App\Services\ASAAS\UseCases\Subscription\UpdateSubscription;
use App\Services\ASAAS\UseCases\Subscription\DeleteSubscription;
use App\Services\ASAAS\UseCases\Subscription\GetSubscriptionPayments;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\UseCases\Subscription\GetSubscriptionById as GetSubscription;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SubscriptionController extends Controller
{
    use Response;

    // TODO: cancel and refunds

    /**
     * Create a subscription
     */
    public function create(Request $request): JsonResponse
    {
        try {
            $subscription_id = $request->input('subscription_id');

            /** @var GetSubscription $getSubscriptionUseCase */
            $getSubscriptionUseCase = app()->make(GetSubscription::class);
            $subscription = $getSubscriptionUseCase->perform($subscription_id);

            /** @var CreateSubscription $useCase */
            $useCase = app()->make(CreateSubscription::class);
            $result = $useCase->perform($subscription);

            return $this->response(
                "Subscription created successfully",
                "success",
                201,
                $result
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get all subscriptions
     */
    public function index(Request $request): JsonResponse
    {
        try {
            /** @var GetAllSubscriptions $useCase */
            $useCase = app()->make(GetAllSubscriptions::class);
            $subscriptions = $useCase->perform($request->all());

            return $this->response(
                "Subscriptions retrieved successfully",
                "success",
                200,
                $subscriptions
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get subscription by ID
     */
    public function show(Request $request): JsonResponse
    {
        try {
            $subscription_id = $request->input('subscription_id');

            /** @var GetSubscription $getSubscriptionUseCase */
            $getSubscriptionUseCase = app()->make(GetSubscription::class);
            $subscription = $getSubscriptionUseCase->perform($subscription_id);

            /** @var GetSubscriptionById $useCase */
            $useCase = app()->make(GetSubscriptionById::class);
            $result = $useCase->perform($subscription);

            return $this->response(
                "Subscription retrieved successfully",
                "success",
                200,
                $result
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Update subscription
     */
    public function update(Request $request): JsonResponse
    {
        try {
            $subscription_id = $request->input('subscription_id');

            /** @var GetSubscription $getSubscriptionUseCase */
            $getSubscriptionUseCase = app()->make(GetSubscription::class);
            $subscription = $getSubscriptionUseCase->perform($subscription_id);

            /** @var UpdateSubscription $useCase */
            $useCase = app()->make(UpdateSubscription::class);
            $result = $useCase->perform($subscription);

            return $this->response(
                "Subscription updated successfully",
                "success",
                200,
                $result
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Delete subscription
     */
    public function destroy(Request $request): JsonResponse
    {
        try {
            $subscription_id = $request->input('subscription_id');

            /** @var GetSubscription $getSubscriptionUseCase */
            $getSubscriptionUseCase = app()->make(GetSubscription::class);
            $subscription = $getSubscriptionUseCase->perform($subscription_id);

            /** @var DeleteSubscription $useCase */
            $useCase = app()->make(DeleteSubscription::class);
            $result = $useCase->perform($subscription);

            return $this->response(
                "Subscription deleted successfully",
                "success",
                200,
                $result
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get subscription payments
     */
    public function getPayments(Request $request): JsonResponse
    {
        try {
            $subscription_id = $request->input('subscription_id');

            /** @var GetSubscription $getSubscriptionUseCase */
            $getSubscriptionUseCase = app()->make(GetSubscription::class);
            $subscription = $getSubscriptionUseCase->perform($subscription_id);

            /** @var GetSubscriptionPayments $useCase */
            $useCase = app()->make(GetSubscriptionPayments::class);
            $payments = $useCase->perform($subscription, $request->all());

            return $this->response(
                "Subscription payments retrieved successfully",
                "success",
                200,
                $payments
            );
        } catch (AsaasException $e) {
            return $this->response(
                $e->getMessage(),
                "error",
                $e->getHttpStatusCode() ?: 400,
                [
                    'asaas_error_code' => $e->getAsaasErrorCode(),
                    'response_data' => $e->getResponseData()
                ]
            );
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}
