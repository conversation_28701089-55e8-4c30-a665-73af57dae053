<?php

namespace App\Services\ASAAS\UseCases\Subscription;

use App\Services\ASAAS\SubscriptionService;
use App\Services\ASAAS\Exceptions\AsaasException;
use App\Domains\Subscription;
use GuzzleHttp\Exception\GuzzleException;

class UpdateSubscription
{
    /**
     * @throws GuzzleException
     * @throws AsaasException
     */
    public function perform(Subscription $subscription): array
    {
        try {
            if (!$subscription->hasAsaasIntegration()) {
                throw new AsaasException("Subscription does not have an ASAAS integration");
            }
            if (!$subscription->hasAsaasCustomer()) {
                throw new AsaasException("Subscription customer does not have an ASAAS customer");
            }

            $organization = ($subscription->is_client_subscription) ? $subscription->client->organization : null;

            $subscriptionService = new SubscriptionService($organization);
            return $subscriptionService->update($subscription->getAsaasSubscriptionId(), $subscription->toAsaasPayload());
        } catch (\Exception $e) {
            throw new AsaasException("Failed to update subscription: {$e->getMessage()}", $e->getCode(), $e);
        }
    }
}
