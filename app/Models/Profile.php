<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Profile extends Model
{
    use SoftDeletes;

    protected $table = 'profiles';

    protected $fillable = [
        'name',
        'slug',
        'description',
        'is_admin',
        'is_super_admin',
    ];

    public function users(){
        return $this->hasMany(User::class);
    }
}
