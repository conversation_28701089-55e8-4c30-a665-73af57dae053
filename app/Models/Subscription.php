<?php

namespace App\Models;

use App\Services\ASAAS\Models\AsaasSubscription;
use Database\Factories\SubscriptionFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

class Subscription extends Model
{
    use HasFactory, SoftDeletes;

    protected static function newFactory()
    {
        return SubscriptionFactory::new();
    }

    protected $fillable = [
        'organization_id',
        'client_id',
        'is_client_subscription',
        'status',
        'billing_type',
        'cycle',
        'value',
        'started_at',
        'expires_at',
        'next_due_date',
        'end_date',
        'is_courtesy',
        'courtesy_expires_at',
        'courtesy_reason',
        'is_trial',
        'trial_expires_at',
        'trial_days',
        'description',
        'max_payments',
        'external_reference',
        'discount_value',
        'discount_type',
        'discount_due_date_limit_days',
        'fine_value',
        'interest_value',
        'deleted',
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'expires_at' => 'datetime',
        'next_due_date' => 'datetime',
        'end_date' => 'datetime',
        'courtesy_expires_at' => 'datetime',
        'trial_expires_at' => 'datetime',
        'is_courtesy' => 'boolean',
        'is_trial' => 'boolean',
        'is_client_subscription' => 'boolean',
        'deleted' => 'boolean',
        'value' => 'decimal:2',
        'discount_value' => 'decimal:2',
        'fine_value' => 'decimal:2',
        'interest_value' => 'decimal:2',
    ];

    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function asaasSubscription(): HasOne
    {
        return $this->hasOne(AsaasSubscription::class);
    }

    public function isActive(): bool
    {
        return $this->status === 'ACTIVE' && !$this->isExpired();
    }

    public function isExpired(): bool
    {
        if ($this->is_courtesy && $this->courtesy_expires_at) {
            return $this->courtesy_expires_at->isPast();
        }

        if ($this->is_trial && $this->trial_expires_at) {
            return $this->trial_expires_at->isPast();
        }

        if ($this->expires_at) {
            return $this->expires_at->isPast();
        }

        return false;
    }

    public function isInCourtesy(): bool
    {
        return $this->is_courtesy &&
               $this->courtesy_expires_at &&
               $this->courtesy_expires_at->isFuture();
    }

    public function isInTrial(): bool
    {
        return $this->is_trial &&
               $this->trial_expires_at &&
               $this->trial_expires_at->isFuture();
    }

    public function canAccessSystem(): bool
    {
        return $this->isActive() || $this->isInCourtesy() || $this->isInTrial();
    }

    public function getDaysUntilExpiration(): int
    {
        $expirationDate = null;

        if ($this->is_courtesy && $this->courtesy_expires_at) {
            $expirationDate = $this->courtesy_expires_at;
        } elseif ($this->is_trial && $this->trial_expires_at) {
            $expirationDate = $this->trial_expires_at;
        } elseif ($this->expires_at) {
            $expirationDate = $this->expires_at;
        }

        if (!$expirationDate) {
            return -1; // No expiration date
        }

        return max(0, now()->diffInDays($expirationDate, false));
    }

    public function needsRenewal(): bool
    {
        $daysUntilExpiration = $this->getDaysUntilExpiration();
        return $daysUntilExpiration >= 0 && $daysUntilExpiration <= 7;
    }

    public function getAccessType(): string
    {
        if ($this->isExpired()) {
            return 'expired';
        }

        if ($this->isInCourtesy()) {
            return 'courtesy';
        }

        if ($this->isInTrial()) {
            return 'trial';
        }

        if ($this->isActive()) {
            return 'paid';
        }

        return 'inactive';
    }

    public function hasAsaasIntegration(): bool
    {
        return $this->asaasSubscription !== null;
    }

    /**
     * Scope to find active subscriptions
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'ACTIVE');
    }

    /**
     * Scope to find expired subscriptions
     */
    public function scopeExpired($query)
    {
        return $query->where(function ($q) {
            $q->where(function ($subQ) {
                $subQ->where('is_courtesy', true)
                     ->where('courtesy_expires_at', '<', now());
            })
            ->orWhere(function ($subQ) {
                $subQ->where('is_trial', true)
                     ->where('trial_expires_at', '<', now());
            })
            ->orWhere(function ($subQ) {
                $subQ->where('is_courtesy', false)
                     ->where('is_trial', false)
                     ->where('expires_at', '<', now());
            });
        });
    }

    /**
     * Scope to find courtesy subscriptions
     */
    public function scopeCourtesy($query)
    {
        return $query->where('is_courtesy', true);
    }

    /**
     * Scope to find trial subscriptions
     */
    public function scopeTrial($query)
    {
        return $query->where('is_trial', true);
    }

    /**
     * Scope to find subscriptions expiring soon
     */
    public function scopeExpiringSoon($query, int $days = 7)
    {
        return $query->where(function ($q) use ($days) {
            $q->where(function ($subQ) use ($days) {
                $subQ->where('is_courtesy', true)
                     ->whereBetween('courtesy_expires_at', [now(), now()->addDays($days)]);
            })
            ->orWhere(function ($subQ) use ($days) {
                $subQ->where('is_trial', true)
                     ->whereBetween('trial_expires_at', [now(), now()->addDays($days)]);
            })
            ->orWhere(function ($subQ) use ($days) {
                $subQ->where('is_courtesy', false)
                     ->where('is_trial', false)
                     ->whereBetween('expires_at', [now(), now()->addDays($days)]);
            });
        });
    }

    /**
     * Scope to find paid subscriptions (not courtesy or trial)
     */
    public function scopePaid($query)
    {
        return $query->where('is_courtesy', false)
                     ->where('is_trial', false);
    }

    /**
     * Convert model to domain
     */
    public function toDomain(): \App\Domains\Subscription
    {
        return new \App\Domains\Subscription(
            id: $this->id,
            organization_id: $this->organization_id,
            status: $this->status,
            billing_type: $this->billing_type,
            cycle: $this->cycle,
            value: (float) $this->value,
            started_at: $this->started_at,
            expires_at: $this->expires_at,
            next_due_date: $this->next_due_date,
            end_date: $this->end_date,
            is_courtesy: $this->is_courtesy,
            courtesy_expires_at: $this->courtesy_expires_at,
            courtesy_reason: $this->courtesy_reason,
            is_trial: $this->is_trial,
            trial_expires_at: $this->trial_expires_at,
            trial_days: $this->trial_days,
            description: $this->description,
            max_payments: $this->max_payments,
            external_reference: $this->external_reference,
            discount_value: $this->discount_value ? (float) $this->discount_value : null,
            discount_type: $this->discount_type,
            discount_due_date_limit_days: $this->discount_due_date_limit_days,
            fine_value: $this->fine_value ? (float) $this->fine_value : null,
            interest_value: $this->interest_value ? (float) $this->interest_value : null,
            deleted: $this->deleted,
            created_at: $this->created_at,
            updated_at: $this->updated_at,
            deleted_at: $this->deleted_at,
        );
    }
}
