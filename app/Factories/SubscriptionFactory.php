<?php

namespace App\Factories;

use App\Domains\Inventory\Client;
use App\Domains\Organization;
use App\Domains\Subscription;
use App\Models\Subscription as SubscriptionModel;
use App\Services\ASAAS\Factories\AsaasSubscriptionFactory;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use App\Factories\Inventory\ClientFactory;

class SubscriptionFactory
{
    public function __construct(
        private AsaasSubscriptionFactory $asaasSubscriptionFactory,
        private OrganizationFactory $organizationFactory,
        private ClientFactory $clientFactory,
    ) {}
    /**
     * Build Subscription domain from Model
     */
    public function buildFromModel(?SubscriptionModel $model): ?Subscription
    {
        if (!$model) {
            return null;
        }

        return new Subscription(
            id: $model->id,
            organization_id: $model->organization_id,
            client_id: $model->client_id,
            is_client_subscription: $model->is_client_subscription,
            status: $model->status,
            billing_type: $model->billing_type,
            cycle: $model->cycle,
            value: (float) $model->value,
            started_at: $model->started_at,
            expires_at: $model->expires_at,
            next_due_date: $model->next_due_date,
            end_date: $model->end_date,
            is_courtesy: $model->is_courtesy,
            courtesy_expires_at: $model->courtesy_expires_at,
            courtesy_reason: $model->courtesy_reason,
            is_trial: $model->is_trial,
            trial_expires_at: $model->trial_expires_at,
            trial_days: $model->trial_days,
            description: $model->description,
            max_payments: $model->max_payments,
            external_reference: $model->external_reference,
            discount_value: $model->discount_value ? (float) $model->discount_value : null,
            discount_type: $model->discount_type,
            discount_due_date_limit_days: $model->discount_due_date_limit_days,
            fine_value: $model->fine_value ? (float) $model->fine_value : null,
            interest_value: $model->interest_value ? (float) $model->interest_value : null,
            deleted: $model->deleted,
            organization: $this->buildOrganizationFromModel($model->organization ?? null),
            client: $this->buildClientFromModel($model->client ?? null),
            asaasSubscription: $this->buildAsaasSubscriptionFromModel($model->asaasSubscription ?? null),
            created_at: $model->created_at,
            updated_at: $model->updated_at,
            deleted_at: $model->deleted_at,
        );
    }

    /**
     * Build multiple Subscription domains from Models
     */
    public function buildFromModels(Collection $models): array
    {
        return $models->map(fn($model) => $this->buildFromModel($model))
                     ->filter()
                     ->values()
                     ->toArray();
    }

    /**
     * Build Subscription domain from store array
     */
    public function buildFromStoreArray(array $data, ?Organization $organization = null): Subscription
    {
        return new Subscription(
            id: $data['id'] ?? null,
            organization_id: $data['organization_id'],
            client_id: $data['client_id'] ?? null,
            is_client_subscription: $data['is_client_subscription'] ?? false,
            status: $data['status'] ?? 'ACTIVE',
            billing_type: $data['billing_type'] ?? 'BOLETO',
            cycle: $data['cycle'] ?? 'MONTHLY',
            value: (float) ($data['value'] ?? 0.00),
            started_at: isset($data['started_at']) ? Carbon::parse($data['started_at']) : now(),
            expires_at: isset($data['expires_at']) ? Carbon::parse($data['expires_at']) : Carbon::now()->addDays($data['days'] ?? 30),
            next_due_date: isset($data['next_due_date']) ? Carbon::parse($data['next_due_date']) : null,
            end_date: isset($data['end_date']) ? Carbon::parse($data['end_date']) : null,
            is_courtesy: $data['is_courtesy'] ?? false,
            courtesy_expires_at: isset($data['courtesy_expires_at']) ? Carbon::parse($data['courtesy_expires_at']) : null,
            courtesy_reason: $data['courtesy_reason'] ?? null,
            is_trial: $data['is_trial'] ?? false,
            trial_expires_at: isset($data['trial_expires_at']) ? Carbon::parse($data['trial_expires_at']) : null,
            trial_days: $data['trial_days'] ?? null,
            description: $data['description'] ?? null,
            max_payments: $data['max_payments'] ?? null,
            external_reference: $data['external_reference'] ?? null,
            discount_value: isset($data['discount_value']) ? (float) $data['discount_value'] : null,
            discount_type: $data['discount_type'] ?? null,
            discount_due_date_limit_days: $data['discount_due_date_limit_days'] ?? null,
            fine_value: isset($data['fine_value']) ? (float) $data['fine_value'] : null,
            interest_value: isset($data['interest_value']) ? (float) $data['interest_value'] : null,
            deleted: $data['deleted'] ?? false,
            organization: $organization,
            client: null,
            created_at: isset($data['created_at']) ? Carbon::parse($data['created_at']) : null,
            updated_at: isset($data['updated_at']) ? Carbon::parse($data['updated_at']) : null,
            deleted_at: isset($data['deleted_at']) ? Carbon::parse($data['deleted_at']) : null,
        );
    }

    /**
     * Build updated Subscription domain with new data
     */
    public function buildUpdated(Subscription $existing, array $data): Subscription
    {
        return new Subscription(
            id: $existing->id,
            organization_id: $existing->organization_id,
            client_id: $data['client_id'] ?? $existing->client_id,
            is_client_subscription: $data['is_client_subscription'] ?? $existing->is_client_subscription,
            status: $data['status'] ?? $existing->status,
            billing_type: $data['billing_type'] ?? $existing->billing_type,
            cycle: $data['cycle'] ?? $existing->cycle,
            value: isset($data['value']) ? (float) $data['value'] : $existing->value,
            started_at: isset($data['started_at']) ? Carbon::parse($data['started_at']) : $existing->started_at,
            expires_at: isset($data['expires_at']) ? Carbon::parse($data['expires_at']) : $existing->expires_at,
            next_due_date: isset($data['next_due_date']) ? Carbon::parse($data['next_due_date']) : $existing->next_due_date,
            end_date: isset($data['end_date']) ? Carbon::parse($data['end_date']) : $existing->end_date,
            is_courtesy: $data['is_courtesy'] ?? $existing->is_courtesy,
            courtesy_expires_at: isset($data['courtesy_expires_at']) ? Carbon::parse($data['courtesy_expires_at']) : $existing->courtesy_expires_at,
            courtesy_reason: $data['courtesy_reason'] ?? $existing->courtesy_reason,
            is_trial: $data['is_trial'] ?? $existing->is_trial,
            trial_expires_at: isset($data['trial_expires_at']) ? Carbon::parse($data['trial_expires_at']) : $existing->trial_expires_at,
            trial_days: $data['trial_days'] ?? $existing->trial_days,
            description: $data['description'] ?? $existing->description,
            max_payments: $data['max_payments'] ?? $existing->max_payments,
            external_reference: $data['external_reference'] ?? $existing->external_reference,
            discount_value: isset($data['discount_value']) ? (float) $data['discount_value'] : $existing->discount_value,
            discount_type: $data['discount_type'] ?? $existing->discount_type,
            discount_due_date_limit_days: $data['discount_due_date_limit_days'] ?? $existing->discount_due_date_limit_days,
            fine_value: isset($data['fine_value']) ? (float) $data['fine_value'] : $existing->fine_value,
            interest_value: isset($data['interest_value']) ? (float) $data['interest_value'] : $existing->interest_value,
            deleted: $data['deleted'] ?? $existing->deleted,
            organization: $existing->organization,
            client: $existing->client,
            asaasSubscription: $existing->asaasSubscription,
            created_at: $existing->created_at,
            updated_at: $existing->updated_at,
            deleted_at: $existing->deleted_at,
        );
    }

    /**
     * Build Organization domain from model (helper method)
     */
    private function buildOrganizationFromModel($organizationModel): ?Organization
    {
        if (!$organizationModel) {
            return null;
        }
        return $this->organizationFactory->buildFromModel($organizationModel, true);
    }

    /**
     * Build AsaasSubscription domain from model (helper method)
     */
    private function buildAsaasSubscriptionFromModel($asaasSubscriptionModel): mixed
    {
        if (!$asaasSubscriptionModel) {
            return null;
        }

        return $this->asaasSubscriptionFactory->buildFromModel($asaasSubscriptionModel);
    }

    private function buildClientFromModel($clientModel): ?Client
    {
        if (!$clientModel) {
            return null;
        }
        return $this->clientFactory->buildFromModel($clientModel, true, false);
    }
}
