<?php

namespace App\Domains;

use App\Domains\Inventory\Client;
use App\Services\ASAAS\Domains\AsaasSubscription;
use Carbon\Carbon;
use Throwable;

class Subscription
{
    public function __construct(
        public ?int               $id,
        public int $organization_id,
        public ?int $client_id,
        public bool $is_client_subscription,
        public string $status,
        public string $billing_type,
        public string $cycle,
        public float $value,
        public Carbon $started_at,
        public ?Carbon $expires_at,
        public ?Carbon $next_due_date,
        public ?Carbon $end_date,
        public bool $is_courtesy,
        public ?Carbon $courtesy_expires_at,
        public ?string $courtesy_reason,
        public bool $is_trial,
        public ?Carbon $trial_expires_at,
        public ?int $trial_days,
        public ?string            $description,
        public ?int               $max_payments,
        public ?string            $external_reference,
        public ?float             $discount_value,
        public ?string            $discount_type,
        public ?int               $discount_due_date_limit_days,
        public ?float             $fine_value,
        public ?float             $interest_value,
        public bool               $deleted,
        public ?Organization      $organization = null,
        public ?Client            $client = null,
        public ?AsaasSubscription $asaasSubscription = null,
        public ?Carbon            $created_at = null,
        public ?Carbon            $updated_at = null,
        public ?Carbon            $deleted_at = null,
    ) {}

    public function isActive(): bool
    {
        return $this->status === 'ACTIVE' && !$this->isExpired();
    }

    public function isExpired(): bool
    {
        if ($this->is_courtesy && $this->courtesy_expires_at) {
            return $this->courtesy_expires_at->isPast();
        }

        if ($this->is_trial && $this->trial_expires_at) {
            return $this->trial_expires_at->isPast();
        }

        if ($this->expires_at) {
            return $this->expires_at->isPast();
        }

        return false;
    }

    public function isInCourtesy(): bool
    {
        return $this->is_courtesy &&
               $this->courtesy_expires_at &&
               $this->courtesy_expires_at->isFuture();
    }

    public function isInTrial(): bool
    {
        return $this->is_trial &&
               $this->trial_expires_at &&
               $this->trial_expires_at->isFuture();
    }

    public function canAccessSystem(): bool
    {
        return $this->isActive() || $this->isInCourtesy() || $this->isInTrial();
    }

    public function getDaysUntilExpiration(): int
    {
        $expirationDate = null;
        if ($this->is_courtesy && $this->courtesy_expires_at) {
            $expirationDate = $this->courtesy_expires_at;
        } elseif ($this->is_trial && $this->trial_expires_at) {
            $expirationDate = $this->trial_expires_at;
        } elseif ($this->expires_at) {
            $expirationDate = $this->expires_at;
        }

        if (!$expirationDate) {
            return -1;
        }

        return max(0, now()->diffInDays($expirationDate, false));
    }

    public function needsRenewal(): bool
    {
        $daysUntilExpiration = $this->getDaysUntilExpiration();
        return $daysUntilExpiration >= 0 && $daysUntilExpiration <= 7;
    }

    public function getAccessType(): string
    {
        if ($this->isExpired()) {
            return 'expired';
        }

        if ($this->isInCourtesy()) {
            return 'courtesy';
        }

        if ($this->isInTrial()) {
            return 'trial';
        }

        if ($this->isActive()) {
            return 'paid';
        }

        return 'inactive';
    }

    public function hasAsaasIntegration(): bool
    {
        return $this->asaasSubscription !== null;
    }

    public function hasAsaasCustomer(): bool
    {
        if ($this->is_client_subscription) {
            return $this->client?->asaas?->hasAsaasIntegration() ?? false;
        } else {
            return $this->organization?->asaas?->hasAsaasIntegration() ?? false;
        }
    }

    public function getAsaasSubscriptionId(): ?string
    {
        return $this->asaasSubscription?->asaas_subscription_id;
    }

    public function isInactive(): bool
    {
        return $this->status === 'INACTIVE';
    }

    public function isSuspended(): bool
    {
        return $this->status === 'SUSPENDED';
    }

    public function isCancelled(): bool
    {
        return $this->status === 'CANCELLED';
    }

    public function isDeleted(): bool
    {
        return $this->deleted;
    }

    public function hasDiscount(): bool
    {
        return $this->discount_value !== null && $this->discount_value > 0;
    }

    public function hasFine(): bool
    {
        return $this->fine_value !== null && $this->fine_value > 0;
    }

    public function hasInterest(): bool
    {
        return $this->interest_value !== null && $this->interest_value > 0;
    }

    public function isMonthly(): bool
    {
        return $this->cycle === 'MONTHLY';
    }

    public function isYearly(): bool
    {
        return $this->cycle === 'YEARLY';
    }

    public function isBoleto(): bool
    {
        return $this->billing_type === 'BOLETO';
    }

    public function isCreditCard(): bool
    {
        return $this->billing_type === 'CREDIT_CARD';
    }

    public function isPix(): bool
    {
        return $this->billing_type === 'PIX';
    }

    /**
     * Convert to array format
     */
    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'organization_id' => $this->organization_id,
            'status' => $this->status,
            'billing_type' => $this->billing_type,
            'cycle' => $this->cycle,
            'value' => $this->value,
            'started_at' => $this->started_at->format('Y-m-d H:i:s'),
            'expires_at' => $this->expires_at?->format('Y-m-d H:i:s'),
            'next_due_date' => $this->next_due_date?->format('Y-m-d H:i:s'),
            'end_date' => $this->end_date?->format('Y-m-d H:i:s'),
            'is_courtesy' => $this->is_courtesy,
            'courtesy_expires_at' => $this->courtesy_expires_at?->format('Y-m-d H:i:s'),
            'courtesy_reason' => $this->courtesy_reason,
            'is_trial' => $this->is_trial,
            'trial_expires_at' => $this->trial_expires_at?->format('Y-m-d H:i:s'),
            'trial_days' => $this->trial_days,
            'description' => $this->description,
            'max_payments' => $this->max_payments,
            'external_reference' => $this->external_reference,
            'discount_value' => $this->discount_value,
            'discount_type' => $this->discount_type,
            'discount_due_date_limit_days' => $this->discount_due_date_limit_days,
            'fine_value' => $this->fine_value,
            'interest_value' => $this->interest_value,
            'deleted' => $this->deleted,
            'organization' => $this->organization?->toArray(),
            'asaas_subscription' => $this->asaasSubscription?->toArray(),
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            'deleted_at' => $this->deleted_at?->format('Y-m-d H:i:s'),
        ];
    }


    public function getCustomerID() : ?string {
        try {
            if ($this->is_client_subscription) {
                return $this->client->asaas->asaas_customer_id;
            } else {
                return $this->organization->asaasCustomer->asaas_customer_id;
            }
        } catch (Throwable $e) {
            return null;
        }
    }

    /**
     * Convert to ASAAS payload format
     */
    public function toAsaasPayload(): array
    {
        $payload = [
            'customer' => $this->getCustomerID(),
            'billingType' => $this->billing_type,
            'cycle' => $this->cycle,
            'value' => $this->value,
            'description' => $this->description ?? "Assinatura #{$this->id}",
            'externalReference' => $this->external_reference ?? (string) $this->id,
        ];

        if ($this->next_due_date) {
            $payload['nextDueDate'] = $this->next_due_date->format('Y-m-d');
        }

        if ($this->end_date) {
            $payload['endDate'] = $this->end_date->format('Y-m-d');
        }

        if ($this->max_payments) {
            $payload['maxPayments'] = $this->max_payments;
        }

        if ($this->discount_value && $this->discount_type) {
            $payload['discount'] = [
                'value' => $this->discount_value,
                'type' => $this->discount_type,
            ];
            if ($this->discount_due_date_limit_days) {
                $payload['discount']['dueDateLimitDays'] = $this->discount_due_date_limit_days;
            }
        }

        if ($this->fine_value) {
            $payload['fine'] = [
                'value' => $this->fine_value,
            ];
        }

        if ($this->interest_value) {
            $payload['interest'] = [
                'value' => $this->interest_value,
            ];
        }

        return $payload;
    }

    /**
     * Convert to store array format (for database insertion)
     */
    public function toStoreArray(): array
    {
        return [
            'organization_id' => $this->organization_id,
            'status' => $this->status,
            'billing_type' => $this->billing_type,
            'cycle' => $this->cycle,
            'value' => $this->value,
            'started_at' => $this->started_at,
            'expires_at' => $this->expires_at,
            'next_due_date' => $this->next_due_date,
            'end_date' => $this->end_date,
            'is_courtesy' => $this->is_courtesy,
            'courtesy_expires_at' => $this->courtesy_expires_at,
            'courtesy_reason' => $this->courtesy_reason,
            'is_trial' => $this->is_trial,
            'trial_expires_at' => $this->trial_expires_at,
            'trial_days' => $this->trial_days,
            'description' => $this->description,
            'max_payments' => $this->max_payments,
            'external_reference' => $this->external_reference,
            'discount_value' => $this->discount_value,
            'discount_type' => $this->discount_type,
            'discount_due_date_limit_days' => $this->discount_due_date_limit_days,
            'fine_value' => $this->fine_value,
            'interest_value' => $this->interest_value,
            'deleted' => $this->deleted,
        ];
    }

    /**
     * Convert to update array format (for database updates)
     */
    public function toUpdateArray(): array
    {
        return [
            'status' => $this->status,
            'billing_type' => $this->billing_type,
            'cycle' => $this->cycle,
            'value' => $this->value,
            'started_at' => $this->started_at,
            'expires_at' => $this->expires_at,
            'next_due_date' => $this->next_due_date,
            'end_date' => $this->end_date,
            'is_courtesy' => $this->is_courtesy,
            'courtesy_expires_at' => $this->courtesy_expires_at,
            'courtesy_reason' => $this->courtesy_reason,
            'is_trial' => $this->is_trial,
            'trial_expires_at' => $this->trial_expires_at,
            'trial_days' => $this->trial_days,
            'description' => $this->description,
            'max_payments' => $this->max_payments,
            'external_reference' => $this->external_reference,
            'discount_value' => $this->discount_value,
            'discount_type' => $this->discount_type,
            'discount_due_date_limit_days' => $this->discount_due_date_limit_days,
            'fine_value' => $this->fine_value,
            'interest_value' => $this->interest_value,
            'deleted' => $this->deleted,
        ];
    }

    /**
     * Get status display name
     */
    public function getStatusDisplayName(): string
    {
        return match ($this->status) {
            'ACTIVE' => 'Ativo',
            'INACTIVE' => 'Inativo',
            'SUSPENDED' => 'Suspenso',
            'CANCELLED' => 'Cancelado',
            'EXPIRED' => 'Expirado',
            default => 'Desconhecido',
        };
    }

    /**
     * Get billing type display name
     */
    public function getBillingTypeDisplayName(): string
    {
        return match ($this->billing_type) {
            'BOLETO' => 'Boleto',
            'CREDIT_CARD' => 'Cartão de Crédito',
            'PIX' => 'PIX',
            'UNDEFINED' => 'Indefinido',
            default => 'Desconhecido',
        };
    }

    /**
     * Get cycle display name
     */
    public function getCycleDisplayName(): string
    {
        return match ($this->cycle) {
            'MONTHLY' => 'Mensal',
            'QUARTERLY' => 'Trimestral',
            'SEMIANNUAL' => 'Semestral',
            'YEARLY' => 'Anual',
            default => 'Desconhecido',
        };
    }

    /**
     * Get discount type display name
     */
    public function getDiscountTypeDisplayName(): ?string
    {
        return match ($this->discount_type) {
            'FIXED' => 'Valor Fixo',
            'PERCENTAGE' => 'Percentual',
            default => null,
        };
    }

    /**
     * Calculate effective value with discount
     */
    public function getEffectiveValue(): float
    {
        if (!$this->hasDiscount()) {
            return $this->value;
        }

        if ($this->discount_type === 'PERCENTAGE') {
            return $this->value * (1 - ($this->discount_value / 100));
        }

        if ($this->discount_type === 'FIXED') {
            return max(0, $this->value - $this->discount_value);
        }

        return $this->value;
    }

    /**
     * Get subscription summary for display
     */
    public function getSummary(): array
    {
        return [
            'access_type' => $this->getAccessType(),
            'status' => $this->getStatusDisplayName(),
            'billing_type' => $this->getBillingTypeDisplayName(),
            'cycle' => $this->getCycleDisplayName(),
            'value' => $this->value,
            'effective_value' => $this->getEffectiveValue(),
            'days_until_expiration' => $this->getDaysUntilExpiration(),
            'needs_renewal' => $this->needsRenewal(),
            'can_access' => $this->canAccessSystem(),
            'has_asaas_integration' => $this->hasAsaasIntegration(),
        ];
    }
}
